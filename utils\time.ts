import { TimeFormat } from '../types/message';

/**
 * 时间格式化和验证工具函数
 */

// 时间格式正则表达式
const FORMAT_12_REGEX = /^(0?[1-9]|1[0-2]):([0-5][0-9])(\s?(AM|PM))?$/i;
const FORMAT_24_REGEX = /^([0-1]?[0-9]|2[0-3]):([0-5][0-9])$/;

/**
 * 格式化显示时间
 * @param time 输入时间字符串
 * @param format 目标格式
 * @returns 格式化后的时间字符串
 */
export const formatDisplayTime = (time: string, format: TimeFormat): string => {
  if (!time.trim()) return time;

  let hours: number;
  let minutes: number;

  if (FORMAT_12_REGEX.test(time)) {
    const match = time.match(FORMAT_12_REGEX);
    if (match) {
      hours = parseInt(match[1]);
      minutes = parseInt(match[2]);
      const isPM = !!(match[4] && match[4].toUpperCase() === 'PM');

      // Convert to 24-hour format first
      if (isPM && hours !== 12) hours += 12;
      if (!isPM && hours === 12) hours = 0;
    } else {
      return time;
    }
  } else if (FORMAT_24_REGEX.test(time)) {
    const match = time.match(FORMAT_24_REGEX);
    if (match) {
      hours = parseInt(match[1]);
      minutes = parseInt(match[2]);
    } else {
      return time;
    }
  } else {
    return time;
  }

  // Format output based on target format
  if (format === '24') {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  } else {
    // 12小时制状态栏时间不显示AM/PM，只显示时间
    const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
    return `${displayHours}:${minutes.toString().padStart(2, '0')}`;
  }
};

/**
 * 验证时间格式
 * @param time 时间字符串
 * @param format 时间格式
 * @returns 验证结果对象
 */
export const validateTimeFormat = (time: string, format: TimeFormat): { isValid: boolean; error: string } => {
  if (!time.trim()) {
    return { isValid: false, error: 'Please enter a time' };
  }

  if (format === '12') {
    if (FORMAT_12_REGEX.test(time) || FORMAT_24_REGEX.test(time)) {
      return { isValid: true, error: '' };
    }
    return { isValid: false, error: 'Please enter a valid 12-hour time format (e.g. 9:41 AM or 9:41)' };
  } else {
    if (FORMAT_24_REGEX.test(time)) {
      return { isValid: true, error: '' };
    }
    return { isValid: false, error: 'Please enter a valid 24-hour time format (e.g. 09:41 or 21:30)' };
  }
};

/**
 * 格式化时间输入（带输入掩码）
 * @param value 输入值
 * @param format 时间格式
 * @returns 格式化后的输入值
 */
export const formatTimeInput = (value: string, format: TimeFormat): string => {
  // Keep only numbers
  const numbersOnly = value.replace(/\D/g, '');

  // Limit to maximum 4 digits
  const limitedNumbers = numbersOnly.slice(0, 4);

  if (limitedNumbers.length === 0) {
    return '';
  }

  if (format === '12') {
    // 12-hour format: allow H:MM format
    if (limitedNumbers.length === 1) {
      return limitedNumbers;
    } else if (limitedNumbers.length === 2) {
      const hours = parseInt(limitedNumbers);
      // If hour > 12, auto-insert colon
      if (hours > 12) {
        return `${limitedNumbers[0]}:${limitedNumbers[1]}`;
      }
      return limitedNumbers;
    } else if (limitedNumbers.length === 3) {
      // Auto-insert colon: H:MM format
      return `${limitedNumbers[0]}:${limitedNumbers.slice(1)}`;
    } else {
      // 4 digits: intelligently determine H:MM or HH:MM
      const firstTwoDigits = parseInt(limitedNumbers.slice(0, 2));
      if (firstTwoDigits >= 10 && firstTwoDigits <= 12) {
        // 10-12 o'clock, use HH:MM format
        return `${limitedNumbers.slice(0, 2)}:${limitedNumbers.slice(2)}`;
      } else {
        // Other cases use H:MM format
        return `${limitedNumbers[0]}:${limitedNumbers.slice(1, 3)}`;
      }
    }
  } else {
    // 24-hour format: force HH:MM format
    if (limitedNumbers.length <= 2) {
      return limitedNumbers;
    } else {
      // Auto-insert colon: HH:MM format
      return `${limitedNumbers.slice(0, 2)}:${limitedNumbers.slice(2)}`;
    }
  }
};

/**
 * 验证时间输入边界值
 * @param hours 小时字符串
 * @param minutes 分钟字符串
 * @param format 时间格式
 * @returns 是否有效
 */
export const isValidTimeInput = (hours: string, minutes: string, format: TimeFormat): boolean => {
  const h = parseInt(hours);
  const m = parseInt(minutes);

  // Validate hour range based on time format
  if (format === '12') {
    // 12-hour format: 1-12
    if (isNaN(h) || h < 1 || h > 12) return false;
  } else {
    // 24-hour format: 0-23
    if (isNaN(h) || h < 0 || h > 23) return false;
  }

  // Minute range 00-59
  if (minutes && (isNaN(m) || m < 0 || m > 59)) return false;

  return true;
};

/**
 * 格式化时间（通用格式化函数）
 * @param time 时间字符串
 * @param targetFormat 目标格式
 * @returns 格式化后的时间
 */
export const formatTime = (time: string, targetFormat: TimeFormat): string => {
  if (!time.trim()) return time;

  let hours: number;
  let minutes: number;
  let isPM = false;

  if (FORMAT_12_REGEX.test(time)) {
    const match = time.match(FORMAT_12_REGEX);
    if (match) {
      hours = parseInt(match[1]);
      minutes = parseInt(match[2]);
      isPM = !!(match[4] && match[4].toUpperCase() === 'PM');

      // Convert to 24-hour format
      if (isPM && hours !== 12) hours += 12;
      if (!isPM && hours === 12) hours = 0;
    } else {
      return time;
    }
  } else if (FORMAT_24_REGEX.test(time)) {
    const match = time.match(FORMAT_24_REGEX);
    if (match) {
      hours = parseInt(match[1]);
      minutes = parseInt(match[2]);
    } else {
      return time;
    }
  } else {
    return time;
  }

  // Format output
  if (targetFormat === '24') {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  } else {
    // 12小时制格式化，根据使用场景决定是否显示AM/PM
    // 注意：状态栏时间使用 formatDisplayTime，不显示AM/PM
    const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
    const ampm = hours >= 12 ? 'PM' : 'AM';
    return `${displayHours}:${minutes.toString().padStart(2, '0')} ${ampm}`;
  }
};
