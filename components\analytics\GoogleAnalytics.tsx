'use client';

import Script from 'next/script';
import { useEffect, useState } from 'react';

interface GoogleAnalyticsProps {
  measurementId: string;
  consentGranted?: boolean;
}

/**
 * GDPR合规的Google Analytics 4组件
 * 
 * 功能：
 * - 符合GDPR要求的分析跟踪
 * - 支持同意模式 (Consent Mode)
 * - 隐私友好的配置
 * - 动态加载和配置
 */
export default function GoogleAnalytics({ measurementId, consentGranted = false }: GoogleAnalyticsProps) {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    if (typeof window !== 'undefined' && window.gtag && isLoaded) {
      // 更新同意状态
      window.gtag('consent', 'update', {
        analytics_storage: consentGranted ? 'granted' : 'denied',
        ad_storage: 'denied', // 始终拒绝广告存储以保护隐私
        ad_user_data: 'denied',
        ad_personalization: 'denied',
      });

      // 如果用户同意，重新配置GA
      if (consentGranted) {
        window.gtag('config', measurementId, {
          // 隐私友好的配置
          anonymize_ip: true,
          allow_google_signals: false,
          allow_ad_personalization_signals: false,
          cookie_expires: 63072000, // 2年
          cookie_update: true,
          cookie_flags: 'SameSite=Strict;Secure',
        });
      }
    }
  }, [consentGranted, measurementId, isLoaded]);

  if (!measurementId) {
    return null;
  }

  return (
    <>
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${measurementId}`}
        strategy="afterInteractive"
        onLoad={() => setIsLoaded(true)}
      />
      <Script id="google-analytics-config" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          
          // 设置默认同意状态（拒绝）
          gtag('consent', 'default', {
            analytics_storage: 'denied',
            ad_storage: 'denied',
            ad_user_data: 'denied',
            ad_personalization: 'denied',
            wait_for_update: 500
          });
          
          gtag('js', new Date());
          
          // 初始配置（在用户同意前不会发送数据）
          gtag('config', '${measurementId}', {
            anonymize_ip: true,
            allow_google_signals: false,
            allow_ad_personalization_signals: false,
            cookie_expires: 63072000,
            cookie_update: true,
            cookie_flags: 'SameSite=Strict;Secure',
            send_page_view: ${consentGranted}
          });
        `}
      </Script>
    </>
  );
}

// 扩展Window接口以包含gtag
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}
