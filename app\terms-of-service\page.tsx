import type { Metada<PERSON> } from "next";
import Link from "next/link";
import Header from "../../components/layout/Header";
import Footer from "../../components/layout/Footer";

export const metadata: Metadata = {
  title: "Terms of Service - Fake Text Message Generator",
  description: "Terms of service for our fake text message generator. Learn about usage restrictions, prohibited activities, and user responsibilities.",
  keywords: [
    "terms of service",
    "terms and conditions",
    "usage policy",
    "fake text generator terms",
    "user agreement",
    "legal terms"
  ],
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    title: "Terms of Service - Fake Text Message Generator",
    description: "Terms of service for our fake text message generator. Learn about usage restrictions, prohibited activities, and user responsibilities.",
    type: "website",
  },
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://faketextmessage.xyz'}/terms-of-service`,
  },
};

export default function TermsOfServicePage() {
  const lastUpdated = "January 14, 2025";

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Breadcrumb */}
        <nav className="mb-8" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-2 text-sm text-gray-500">
            <li>
              <Link href="/" className="hover:text-blue-600 transition-colors">
                Home
              </Link>
            </li>
            <li className="flex items-center">
              <svg className="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              <span className="text-gray-900">Terms of Service</span>
            </li>
          </ol>
        </nav>

        {/* Header */}
        <div className="mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Terms of Service
          </h1>
          <p className="text-lg text-gray-600 mb-2">
            Please read these terms carefully before using our fake text message generator.
          </p>
          <p className="text-sm text-gray-500">
            Last updated: {lastUpdated}
          </p>
        </div>

        {/* Content */}
        <div className="prose prose-lg max-w-none">
          {/* Important Notice */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-red-900 mb-3 mt-0">
              ⚠️ Important Notice
            </h2>
            <p className="text-red-800 mb-0">
              This tool is designed for <strong>legitimate creative purposes only</strong>. Any use for fraud, deception, harassment, or illegal activities is strictly prohibited and may result in legal consequences.
            </p>
          </div>

          {/* Acceptance of Terms */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              1. Acceptance of Terms
            </h2>
            <p className="text-gray-700 mb-4">
              By accessing and using the Fake Text Message Generator ("Service"), you accept and agree to be bound by the terms and provision of this agreement.
            </p>
            <p className="text-gray-700">
              If you do not agree to abide by the above, please do not use this service.
            </p>
          </section>

          {/* Permitted Uses */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              2. Permitted Uses
            </h2>
            <p className="text-gray-700 mb-4">
              You may use our service for the following legitimate purposes:
            </p>
            <ul className="list-disc pl-6 space-y-2 text-gray-700">
              <li><strong>Creative Content:</strong> Creating content for videos, stories, or artistic projects</li>
              <li><strong>Educational Purposes:</strong> Teaching about digital literacy or social media awareness</li>
              <li><strong>Design Mockups:</strong> Creating app interface mockups or design prototypes</li>
              <li><strong>Entertainment:</strong> Creating fictional conversations for entertainment purposes</li>
              <li><strong>Testing:</strong> Testing app layouts or user interface designs</li>
              <li><strong>Personal Projects:</strong> Creating content for personal, non-commercial use</li>
            </ul>
          </section>

          {/* Prohibited Uses */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              3. Prohibited Uses
            </h2>
            <p className="text-gray-700 mb-4">
              You are strictly prohibited from using our service for:
            </p>
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <ul className="list-disc pl-6 space-y-2 text-red-800">
                <li><strong>Fraud or Deception:</strong> Creating fake evidence for legal proceedings or insurance claims</li>
                <li><strong>Harassment:</strong> Creating fake conversations to harass, bully, or intimidate others</li>
                <li><strong>Identity Theft:</strong> Impersonating others without their consent</li>
                <li><strong>Defamation:</strong> Creating false statements that damage someone's reputation</li>
                <li><strong>Scams:</strong> Creating fake conversations to deceive or defraud others</li>
                <li><strong>Cyberbullying:</strong> Using fake messages to harm or intimidate individuals</li>
                <li><strong>Revenge Porn:</strong> Creating fake intimate conversations or compromising content</li>
                <li><strong>Misinformation:</strong> Spreading false information or conspiracy theories</li>
                <li><strong>Illegal Activities:</strong> Any use that violates local, state, or federal laws</li>
              </ul>
            </div>
          </section>

          {/* User Responsibilities */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              4. User Responsibilities
            </h2>
            <p className="text-gray-700 mb-4">
              As a user of this service, you agree to:
            </p>
            <ul className="list-disc pl-6 space-y-2 text-gray-700">
              <li>Use the service only for lawful and legitimate purposes</li>
              <li>Respect the rights and privacy of others</li>
              <li>Not use the service to create content that could harm others</li>
              <li>Take full responsibility for any content you create</li>
              <li>Comply with all applicable laws and regulations</li>
              <li>Not attempt to reverse engineer or copy our service</li>
            </ul>
          </section>

          {/* Intellectual Property */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              5. Intellectual Property Rights
            </h2>
            <p className="text-gray-700 mb-4">
              <strong>Our Service:</strong> The Fake Text Message Generator, including its design, code, and functionality, is protected by copyright and other intellectual property laws.
            </p>
            <p className="text-gray-700 mb-4">
              <strong>Your Content:</strong> You retain ownership of any content you create using our service. However, you are solely responsible for ensuring your content doesn't infringe on others' rights.
            </p>
            <p className="text-gray-700">
              <strong>Third-Party Content:</strong> You must have proper rights to any images, names, or other content you use in your generated messages.
            </p>
          </section>

          {/* Disclaimers */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              6. Disclaimers
            </h2>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
              <ul className="list-disc pl-6 space-y-2 text-yellow-800">
                <li>The service is provided "as is" without warranties of any kind</li>
                <li>We do not guarantee uninterrupted or error-free operation</li>
                <li>We are not responsible for how you use the generated content</li>
                <li>We do not endorse or verify any user-generated content</li>
                <li>The service may be modified or discontinued at any time</li>
              </ul>
            </div>
          </section>

          {/* Limitation of Liability */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              7. Limitation of Liability
            </h2>
            <p className="text-gray-700 mb-4">
              To the maximum extent permitted by law:
            </p>
            <ul className="list-disc pl-6 space-y-2 text-gray-700">
              <li>We shall not be liable for any indirect, incidental, or consequential damages</li>
              <li>Our total liability shall not exceed the amount you paid for the service (which is $0 for our free service)</li>
              <li>You agree to indemnify us against any claims arising from your use of the service</li>
              <li>We are not responsible for any legal consequences of your use of generated content</li>
            </ul>
          </section>

          {/* Privacy */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              8. Privacy
            </h2>
            <p className="text-gray-700">
              Your privacy is important to us. Please review our <Link href="/privacy-policy" className="text-blue-600 hover:text-blue-800">Privacy Policy</Link> to understand how we handle your information. In summary, we process everything locally and don't collect or store personal data.
            </p>
          </section>

          {/* Termination */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              9. Termination
            </h2>
            <p className="text-gray-700 mb-4">
              We reserve the right to:
            </p>
            <ul className="list-disc pl-6 space-y-2 text-gray-700">
              <li>Terminate or suspend access to our service at any time</li>
              <li>Modify or discontinue the service without notice</li>
              <li>Block access from users who violate these terms</li>
            </ul>
            <p className="text-gray-700 mt-4">
              You may stop using our service at any time.
            </p>
          </section>

          {/* Governing Law */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              10. Governing Law
            </h2>
            <p className="text-gray-700">
              These terms shall be governed by and construed in accordance with the laws of the jurisdiction where our service is operated, without regard to conflict of law principles.
            </p>
          </section>

          {/* Changes to Terms */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              11. Changes to Terms
            </h2>
            <p className="text-gray-700">
              We reserve the right to modify these terms at any time. Changes will be posted on this page with an updated "Last modified" date. Your continued use of the service after changes constitutes acceptance of the new terms.
            </p>
          </section>

          {/* Contact Information */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              12. Contact Information
            </h2>
            <p className="text-gray-700 mb-4">
              If you have questions about these terms, please contact us:
            </p>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
              <ul className="space-y-2 text-gray-700">
                <li><strong>Email:</strong> <EMAIL></li>
                <li><strong>Contact Page:</strong> <Link href="/contact" className="text-blue-600 hover:text-blue-800">faketextmessage.xyz/contact</Link></li>
              </ul>
            </div>
          </section>

          {/* Acknowledgment */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">
              Acknowledgment
            </h3>
            <p className="text-blue-800 mb-0">
              By using our service, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service.
            </p>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <Link 
              href="/privacy-policy"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              View Privacy Policy
            </Link>
            <Link 
              href="/disclaimer"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
            >
              View Disclaimer
              <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
