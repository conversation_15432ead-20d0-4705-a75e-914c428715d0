/**
 * 无障碍功能测试脚本
 * 在浏览器控制台中运行，检查无障碍功能实现
 */

class AccessibilityTester {
  constructor() {
    this.results = [];
    this.errors = [];
  }

  /**
   * 记录测试结果
   */
  log(test, passed, message = '') {
    const result = {
      test,
      passed,
      message,
      timestamp: new Date().toISOString()
    };
    this.results.push(result);
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${test}: ${message}`);
    
    if (!passed) {
      this.errors.push(result);
    }
  }

  /**
   * 检查ARIA标签
   */
  testAriaLabels() {
    console.log('🔍 检查ARIA标签...');
    
    // 检查主要区域的ARIA标签
    const main = document.querySelector('main');
    this.log('主内容区域ARIA标签', 
      main && (main.hasAttribute('role') || main.hasAttribute('aria-label')),
      main ? '主内容区域有适当的ARIA标签' : '未找到主内容区域'
    );

    // 检查导航区域
    const nav = document.querySelector('nav');
    this.log('导航区域ARIA标签',
      nav && nav.hasAttribute('aria-label'),
      nav ? '导航区域有aria-label' : '未找到导航区域'
    );

    // 检查按钮的ARIA标签
    const buttons = document.querySelectorAll('button');
    let buttonsWithLabels = 0;
    buttons.forEach(button => {
      if (button.hasAttribute('aria-label') || 
          button.hasAttribute('aria-labelledby') || 
          button.textContent.trim() !== '') {
        buttonsWithLabels++;
      }
    });
    
    this.log('按钮ARIA标签',
      buttonsWithLabels === buttons.length,
      `${buttonsWithLabels}/${buttons.length} 按钮有适当的标签`
    );

    // 检查输入框的标签
    const inputs = document.querySelectorAll('input, textarea, select');
    let inputsWithLabels = 0;
    inputs.forEach(input => {
      if (input.hasAttribute('aria-label') || 
          input.hasAttribute('aria-labelledby') ||
          document.querySelector(`label[for="${input.id}"]`)) {
        inputsWithLabels++;
      }
    });

    this.log('输入框标签',
      inputsWithLabels === inputs.length,
      `${inputsWithLabels}/${inputs.length} 输入框有适当的标签`
    );
  }

  /**
   * 检查键盘导航
   */
  testKeyboardNavigation() {
    console.log('⌨️ 检查键盘导航...');
    
    // 检查可聚焦元素
    const focusableElements = document.querySelectorAll(
      'button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), a[href], [tabindex]:not([tabindex="-1"])'
    );
    
    this.log('可聚焦元素',
      focusableElements.length > 0,
      `找到 ${focusableElements.length} 个可聚焦元素`
    );

    // 检查跳转链接
    const skipLink = document.querySelector('a[href="#main-content"], a[href*="skip"]');
    this.log('跳转链接',
      skipLink !== null,
      skipLink ? '存在跳转到主内容的链接' : '缺少跳转链接'
    );

    // 检查焦点指示器
    let hasFocusStyles = false;
    try {
      const testElement = focusableElements[0];
      if (testElement) {
        testElement.focus();
        const computedStyle = window.getComputedStyle(testElement, ':focus');
        hasFocusStyles = computedStyle.outline !== 'none' || 
                        computedStyle.boxShadow !== 'none' ||
                        testElement.classList.contains('focus-visible');
      }
    } catch (e) {
      // 忽略错误
    }

    this.log('焦点指示器',
      hasFocusStyles,
      hasFocusStyles ? '元素有焦点指示器' : '可能缺少焦点指示器'
    );
  }

  /**
   * 检查语义化HTML
   */
  testSemanticHTML() {
    console.log('📝 检查语义化HTML...');
    
    // 检查标题层次
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    this.log('标题元素',
      headings.length > 0,
      `找到 ${headings.length} 个标题元素`
    );

    // 检查列表结构
    const lists = document.querySelectorAll('ul, ol, dl');
    this.log('列表结构',
      lists.length >= 0,
      `找到 ${lists.length} 个列表元素`
    );

    // 检查表单结构
    const forms = document.querySelectorAll('form');
    const labels = document.querySelectorAll('label');
    this.log('表单结构',
      forms.length === 0 || labels.length > 0,
      forms.length > 0 ? `${forms.length} 个表单，${labels.length} 个标签` : '无表单元素'
    );

    // 检查图片alt属性
    const images = document.querySelectorAll('img');
    let imagesWithAlt = 0;
    images.forEach(img => {
      if (img.hasAttribute('alt')) {
        imagesWithAlt++;
      }
    });

    this.log('图片alt属性',
      images.length === 0 || imagesWithAlt === images.length,
      `${imagesWithAlt}/${images.length} 图片有alt属性`
    );
  }

  /**
   * 检查颜色对比度
   */
  testColorContrast() {
    console.log('🎨 检查颜色对比度...');
    
    // 检查主要文本元素的对比度
    const textElements = document.querySelectorAll('p, span, div, button, a, h1, h2, h3, h4, h5, h6');
    let contrastIssues = 0;
    
    textElements.forEach(element => {
      const style = window.getComputedStyle(element);
      const color = style.color;
      const backgroundColor = style.backgroundColor;
      
      // 简单的对比度检查（这里只是示例，实际需要更复杂的计算）
      if (color === backgroundColor || 
          (color === 'rgb(255, 255, 255)' && backgroundColor === 'rgb(255, 255, 255)')) {
        contrastIssues++;
      }
    });

    this.log('颜色对比度',
      contrastIssues === 0,
      contrastIssues > 0 ? `发现 ${contrastIssues} 个潜在对比度问题` : '未发现明显对比度问题'
    );
  }

  /**
   * 检查屏幕阅读器支持
   */
  testScreenReaderSupport() {
    console.log('📢 检查屏幕阅读器支持...');
    
    // 检查实时区域
    const liveRegions = document.querySelectorAll('[aria-live]');
    this.log('实时区域',
      liveRegions.length > 0,
      `找到 ${liveRegions.length} 个实时区域`
    );

    // 检查隐藏内容
    const srOnlyElements = document.querySelectorAll('.sr-only, .visually-hidden');
    this.log('屏幕阅读器专用内容',
      srOnlyElements.length > 0,
      `找到 ${srOnlyElements.length} 个屏幕阅读器专用元素`
    );

    // 检查ARIA描述
    const elementsWithDescription = document.querySelectorAll('[aria-describedby]');
    this.log('ARIA描述',
      elementsWithDescription.length >= 0,
      `${elementsWithDescription.length} 个元素有ARIA描述`
    );

    // 检查角色定义
    const elementsWithRoles = document.querySelectorAll('[role]');
    this.log('ARIA角色',
      elementsWithRoles.length > 0,
      `${elementsWithRoles.length} 个元素定义了ARIA角色`
    );
  }

  /**
   * 检查移动端无障碍
   */
  testMobileAccessibility() {
    console.log('📱 检查移动端无障碍...');
    
    // 检查触摸目标大小
    const interactiveElements = document.querySelectorAll('button, a, input, select, textarea');
    let smallTargets = 0;
    
    interactiveElements.forEach(element => {
      const rect = element.getBoundingClientRect();
      if (rect.width < 44 || rect.height < 44) {
        smallTargets++;
      }
    });

    this.log('触摸目标大小',
      smallTargets === 0,
      smallTargets > 0 ? `${smallTargets} 个元素可能太小` : '所有交互元素大小适当'
    );

    // 检查视口配置
    const viewport = document.querySelector('meta[name="viewport"]');
    this.log('视口配置',
      viewport !== null,
      viewport ? '有视口配置' : '缺少视口配置'
    );
  }

  /**
   * 运行所有测试
   */
  runAllTests() {
    console.log('🚀 开始无障碍功能测试...\n');
    
    this.testAriaLabels();
    this.testKeyboardNavigation();
    this.testSemanticHTML();
    this.testColorContrast();
    this.testScreenReaderSupport();
    this.testMobileAccessibility();
    
    this.generateReport();
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    console.log('\n📊 无障碍测试报告');
    console.log('='.repeat(50));
    
    const total = this.results.length;
    const passed = this.results.filter(r => r.passed).length;
    const failed = this.errors.length;
    
    console.log(`总测试数: ${total}`);
    console.log(`通过: ${passed} ✅`);
    console.log(`失败: ${failed} ❌`);
    console.log(`成功率: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (this.errors.length > 0) {
      console.log('\n❌ 需要改进的项目:');
      this.errors.forEach(error => {
        console.log(`  • ${error.test}: ${error.message}`);
      });
    }
    
    console.log('\n💡 无障碍改进建议:');
    if (failed === 0) {
      console.log('  • 无障碍功能基础良好！');
      console.log('  • 建议进行真实用户测试');
      console.log('  • 考虑使用专业无障碍测试工具');
    } else {
      console.log('  • 优先修复失败的测试项目');
      console.log('  • 参考WCAG 2.1指南进行改进');
      console.log('  • 使用屏幕阅读器进行实际测试');
    }
    
    return {
      total,
      passed,
      failed,
      successRate: (passed / total) * 100,
      errors: this.errors,
      results: this.results
    };
  }
}

// 使用说明
console.log(`
♿ 无障碍功能测试脚本使用说明:

1. 在浏览器控制台中运行以下命令开始测试:
   const tester = new AccessibilityTester();
   tester.runAllTests();

2. 或者运行单个测试:
   tester.testAriaLabels();
   tester.testKeyboardNavigation();

3. 查看详细结果:
   console.table(tester.results);
`);

// 自动运行（可选）
if (typeof window !== 'undefined' && window.location.hostname !== '') {
  console.log('🔄 自动运行无障碍测试...');
  const autoTester = new AccessibilityTester();
  autoTester.runAllTests();
}
