import React, { useMemo } from 'react';
import { TimelineProps } from '../../types/changelog';
import TimelineEntry from './TimelineEntry';
import FilterBar from './FilterBar';
import SearchBar from './SearchBar';
import { updateTypeConfigs } from '../../data/changelog';

/**
 * 时间轴主组件
 * 管理更新历史的显示、筛选和搜索
 */
const Timeline: React.FC<TimelineProps> = ({
  entries,
  filter,
  searchQuery,
  onFilterChange,
  onSearchChange,
  onToggleExpand
}) => {
  // 计算各类型的数量并更新配置
  const updatedTypeConfigs = useMemo(() => {
    return updateTypeConfigs.map(config => ({
      ...config,
      count: entries.filter(entry => entry.type === config.type).length
    }));
  }, [entries]);

  // 筛选和搜索逻辑
  const filteredEntries = useMemo(() => {
    let filtered = entries;

    // 按类型筛选
    if (filter !== 'all') {
      filtered = filtered.filter(entry => entry.type === filter);
    }

    // 按搜索关键词筛选
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(entry => 
        entry.title.toLowerCase().includes(query) ||
        entry.description.toLowerCase().includes(query) ||
        (entry.details && entry.details.some(detail => 
          detail.toLowerCase().includes(query)
        )) ||
        (entry.version && entry.version.toLowerCase().includes(query))
      );
    }

    return filtered;
  }, [entries, filter, searchQuery]);

  // Empty state component
  const EmptyState = () => (
    <div className="text-center py-12">
      <svg
        className="mx-auto h-12 w-12 text-gray-400"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        aria-hidden="true"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
        />
      </svg>
      <h3 className="mt-2 text-sm font-medium text-gray-900">
        No matching update records found
      </h3>
      <p className="mt-1 text-sm text-gray-500">
        {searchQuery ? 'Try using different search keywords' : 'Try adjusting the filter criteria'}
      </p>
      {(searchQuery || filter !== 'all') && (
        <button
          onClick={() => {
            onSearchChange('');
            onFilterChange('all');
          }}
          className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-100 hover:bg-blue-200 transition-colors duration-200"
        >
          Clear All Filters
        </button>
      )}
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto">
      {/* Search Bar */}
      <SearchBar
        searchQuery={searchQuery}
        onSearchChange={onSearchChange}
        placeholder="Search updates, version numbers, or keywords..."
      />

      {/* Filter Bar */}
      <FilterBar
        activeFilter={filter}
        updateTypes={updatedTypeConfigs}
        onFilterChange={onFilterChange}
      />

      {/* Results Statistics */}
      <div className="mb-6 text-sm text-gray-600">
        {searchQuery || filter !== 'all' ? (
          <>
            Found <span className="font-medium text-gray-900">{filteredEntries.length}</span> update records
            {searchQuery && (
              <span> containing "<span className="font-medium">{searchQuery}</span>"</span>
            )}
            {filter !== 'all' && (
              <span> of type "<span className="font-medium">{updatedTypeConfigs.find(c => c.type === filter)?.label}</span>"</span>
            )}
          </>
        ) : (
          <>Total <span className="font-medium text-gray-900">{entries.length}</span> update records</>
        )}
      </div>

      {/* Timeline Content */}
      {filteredEntries.length > 0 ? (
        <div className="space-y-0">
          {filteredEntries.map((entry, index) => (
            <TimelineEntry
              key={entry.id}
              entry={entry}
              onToggleExpand={onToggleExpand}
              isLast={index === filteredEntries.length - 1}
            />
          ))}
        </div>
      ) : (
        <EmptyState />
      )}
    </div>
  );
};

export default Timeline;
