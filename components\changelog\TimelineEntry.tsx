import React from 'react';
import { TimelineEntryProps } from '../../types/changelog';
import { updateTypeConfigs } from '../../data/changelog';

/**
 * Timeline entry component
 * Displays a single update record with expand/collapse details functionality
 */
const TimelineEntry: React.FC<TimelineEntryProps> = ({
  entry,
  onToggleExpand,
  isLast
}) => {
  // Get update type configuration
  const typeConfig = updateTypeConfigs.find(config => config.type === entry.type);

  // Format date display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Format relative time
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
    return `${Math.floor(diffInDays / 365)} years ago`;
  };

  return (
    <div className="relative flex items-start space-x-4 pb-8">
      {/* Timeline Line */}
      {!isLast && (
        <div className="absolute left-6 top-12 w-0.5 h-full bg-gray-200" />
      )}

      {/* Timeline Node */}
      <div className={`relative z-10 flex items-center justify-center w-12 h-12 rounded-full ${typeConfig?.bgColor || 'bg-gray-100'} border-4 border-white shadow-sm`}>
        <svg
          className={`w-6 h-6 ${typeConfig?.color || 'text-gray-600'}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d={typeConfig?.icon || 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'}
          />
        </svg>
      </div>

      {/* Content Area */}
      <div className="flex-1 min-w-0">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
          {/* Header Information */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                {/* Version Number */}
                {entry.version && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {entry.version}
                  </span>
                )}

                {/* Update Type Label */}
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${typeConfig?.bgColor || 'bg-gray-100'} ${typeConfig?.color || 'text-gray-600'}`}>
                  {typeConfig?.label || entry.type}
                </span>
              </div>

              {/* Title */}
              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                {entry.title}
              </h3>
            </div>

            {/* Date Information */}
            <div className="text-right text-sm text-gray-500 ml-4">
              <div className="font-medium">{formatRelativeTime(entry.date)}</div>
              <div className="text-xs">{formatDate(entry.date)}</div>
            </div>
          </div>

          {/* Description */}
          <p className="text-gray-700 mb-4 leading-relaxed">
            {entry.description}
          </p>

          {/* Details Expand/Collapse */}
          {entry.details && entry.details.length > 0 && (
            <>
              <button
                onClick={() => onToggleExpand(entry.id)}
                className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-500 transition-colors duration-200"
                aria-expanded={entry.isExpanded}
              >
                {entry.isExpanded ? 'Hide Details' : 'Show Details'}
                <svg
                  className={`ml-1 w-4 h-4 transform transition-transform duration-200 ${
                    entry.isExpanded ? 'rotate-180' : ''
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {/* Details Content */}
              {entry.isExpanded && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <ul className="space-y-2">
                    {entry.details.map((detail, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <div className="w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 flex-shrink-0" />
                        <span className="text-sm text-gray-600 leading-relaxed">
                          {detail}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default TimelineEntry;
