export default function Loading() {
  return (
    <div className="min-h-screen bg-white flex flex-col items-center justify-center py-12 sm:py-16 lg:py-20">
      {/* Loading Spinner */}
      <div className="mb-8">
        <div className="relative">
          {/* Outer Ring */}
          <div className="w-16 h-16 border-4 border-gray-200 rounded-full"></div>
          {/* Spinning Ring */}
          <div className="absolute top-0 left-0 w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>

      {/* Loading Text */}
      <div className="text-center">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Loading...
        </h2>
        <p className="text-gray-600">
          Please wait while we prepare your content
        </p>
      </div>

      {/* Loading Animation Dots */}
      <div className="flex space-x-1 mt-6">
        <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
        <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
        <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
      </div>
    </div>
  );
}
