import React from 'react';

interface SignalIconProps {
  strength?: 1 | 2 | 3 | 4;
  className?: string;
  color?: string;
}

const SignalIcon: React.FC<SignalIconProps> = ({
  strength = 4,
  className = '',
  color = 'currentColor'
}) => {
  return (
    <div className={`flex items-end space-x-0.5 ${className}`}>
      {/* 信号条1 */}
      <div 
        className={`w-1 h-2 rounded-full ${strength >= 1 ? 'opacity-100' : 'opacity-30'}`}
        style={{ backgroundColor: color }}
      ></div>
      
      {/* 信号条2 */}
      <div 
        className={`w-1 h-2.5 rounded-full ${strength >= 2 ? 'opacity-100' : 'opacity-30'}`}
        style={{ backgroundColor: color }}
      ></div>
      
      {/* 信号条3 */}
      <div 
        className={`w-1 h-3 rounded-full ${strength >= 3 ? 'opacity-100' : 'opacity-30'}`}
        style={{ backgroundColor: color }}
      ></div>
      
      {/* 信号条4 */}
      <div 
        className={`w-1 h-3.5 rounded-full ${strength >= 4 ? 'opacity-100' : 'opacity-30'}`}
        style={{ backgroundColor: color }}
      ></div>
    </div>
  );
};

export default SignalIcon;
