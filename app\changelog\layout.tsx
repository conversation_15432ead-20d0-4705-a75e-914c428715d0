import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Update History - Fake Text Message Generator',
  description: 'Track all feature updates, improvements, and fixes for the Fake Text Message Generator. Stay informed about the latest developments and enhancements.',
  keywords: [
    'changelog',
    'update history',
    'version history',
    'release notes',
    'fake text message updates',
    'feature updates',
    'bug fixes',
    'improvements'
  ],
  openGraph: {
    title: 'Update History - Fake Text Message Generator',
    description: 'Track all feature updates, improvements, and fixes for the Fake Text Message Generator.',
    type: 'website',
    url: '/changelog',
  },
  twitter: {
    card: 'summary',
    title: 'Update History - Fake Text Message Generator',
    description: 'Track all feature updates, improvements, and fixes for the Fake Text Message Generator.',
  },
  alternates: {
    canonical: '/changelog',
  },
};

export default function ChangelogLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
