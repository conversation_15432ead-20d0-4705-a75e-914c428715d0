import { useState, useCallback } from 'react';
import { TimeFormat } from '../types/message';
import { 
  validateTimeFormat, 
  formatTimeInput, 
  isValidTimeInput 
} from '../utils/time';

/**
 * 自定义Hook用于处理时间输入的状态和验证
 */
export const useTimeInput = (initialTime: string = '9:41', initialFormat: TimeFormat = '12') => {
  const [deviceTime, setDeviceTime] = useState(initialTime);
  const [timeFormat, setTimeFormat] = useState<TimeFormat>(initialFormat);
  const [timeError, setTimeError] = useState<string>('');
  const [isTimeValid, setIsTimeValid] = useState(true);

  // Handle formatted time input changes
  const handleFormattedTimeChange = useCallback((value: string) => {
    // Format input
    const formattedValue = formatTimeInput(value, timeFormat);

    // Validate boundary values
    if (formattedValue.includes(':')) {
      const [hours, minutes] = formattedValue.split(':');
      if (!isValidTimeInput(hours, minutes || '', timeFormat)) {
        // If boundary values are invalid, don't update state
        return;
      }
    } else if (formattedValue.length >= 1) {
      // Validate hour part (may be 1 or 2 digits)
      if (!isValidTimeInput(formattedValue, '', timeFormat)) {
        return;
      }
    }

    // Update device time
    setDeviceTime(formattedValue);

    // Validate complete time format
    const isComplete = formattedValue.includes(':') &&
                      (formattedValue.length === 4 || formattedValue.length === 5);

    if (isComplete) {
      const validation = validateTimeFormat(formattedValue, timeFormat);
      setIsTimeValid(validation.isValid);
      setTimeError(validation.error);
    } else if (formattedValue.length === 0) {
      setIsTimeValid(true);
      setTimeError('');
    } else {
      // Input incomplete, temporarily mark as valid but don't show error
      setIsTimeValid(true);
      setTimeError('');
    }
  }, [timeFormat]);

  // Handle time format changes
  const handleTimeFormatChange = useCallback((newFormat: TimeFormat) => {
    setTimeFormat(newFormat);

    // If current time is valid, keep it as is - the display will automatically update
    // due to the formatDisplayTime function in the status bar
    if (deviceTime.trim()) {
      const validation = validateTimeFormat(deviceTime, newFormat);
      setIsTimeValid(validation.isValid);
      setTimeError(validation.error);
    }
  }, [deviceTime]);

  // Reset time to default
  const resetTime = useCallback(() => {
    setDeviceTime('9:41');
    setTimeFormat('12');
    setTimeError('');
    setIsTimeValid(true);
  }, []);

  return {
    deviceTime,
    timeFormat,
    timeError,
    isTimeValid,
    handleFormattedTimeChange,
    handleTimeFormatChange,
    resetTime,
    setDeviceTime,
    setTimeFormat
  };
};
