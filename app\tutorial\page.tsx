'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import Header from '../../components/layout/Header';
import Footer from '../../components/layout/Footer';
import TutorialOverlay from '../../components/tutorial/TutorialOverlay';
import PhonePreview from '../../components/device/PhonePreview';
import MessageInput from '../../components/ui/MessageInput';
import { TutorialStep, TutorialState, Message, ThemeMode, TimeFormat } from '../../types/message';
import { useTimeInput } from '../../hooks/useTimeInput';
import { useNotification } from '../../components/ui/notification';
import { tutorialSteps, tutorialConfig } from '../../data/tutorialSteps';

/**
 * 教程页面主组件
 * 提供完整的交互式教程体验
 */
export default function TutorialPage() {
  // 教程状态管理
  const [tutorialState, setTutorialState] = useState<TutorialState>({
    isActive: false,
    currentStepIndex: 0,
    completedSteps: [],
    skippedSteps: [],
    startTime: undefined,
    endTime: undefined
  });

  // 演示数据状态 - 与主页面保持一致的初始值
  const [recipientName, setRecipientName] = useState('fake text message');
  const [recipientAvatar, setRecipientAvatar] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [mode, setMode] = useState<ThemeMode>('light');
  const [batteryPercentage, setBatteryPercentage] = useState(100);
  
  // 时间管理
  const {
    deviceTime,
    timeFormat,
    setDeviceTime,
    setTimeFormat
  } = useTimeInput();

  // 通知系统
  const { toast } = useNotification();

  // 引用
  const pageRef = useRef<HTMLDivElement>(null);
  const phonePreviewRef = useRef<HTMLDivElement>(null);

  // 获取当前步骤
  const currentStep = tutorialSteps[tutorialState.currentStepIndex] || null;

  // 开始教程
  const startTutorial = useCallback(() => {
    setTutorialState(prev => ({
      ...prev,
      isActive: true,
      currentStepIndex: 0,
      completedSteps: [],
      skippedSteps: [],
      startTime: new Date()
    }));
  }, []);

  // 自动启动教程
  useEffect(() => {
    // 页面加载后延迟1秒自动开始教程，给用户一点时间适应页面
    const timer = setTimeout(() => {
      startTutorial();
    }, 1000);

    return () => clearTimeout(timer);
  }, [startTutorial]);

  // 下一步
  const nextStep = useCallback(() => {
    if (tutorialState.currentStepIndex < tutorialSteps.length - 1) {
      setTutorialState(prev => ({
        ...prev,
        currentStepIndex: prev.currentStepIndex + 1,
        completedSteps: [...prev.completedSteps, currentStep?.id || '']
      }));
    } else {
      // 教程完成
      setTutorialState(prev => ({
        ...prev,
        isActive: false,
        endTime: new Date()
      }));
      toast.success('教程完成！您已掌握所有功能。');
    }
  }, [tutorialState.currentStepIndex, currentStep?.id, toast]);

  // 上一步
  const prevStep = useCallback(() => {
    if (tutorialState.currentStepIndex > 0) {
      setTutorialState(prev => ({
        ...prev,
        currentStepIndex: prev.currentStepIndex - 1
      }));
    }
  }, [tutorialState.currentStepIndex]);

  // 跳过步骤
  const skipStep = useCallback(() => {
    if (currentStep?.isSkippable) {
      setTutorialState(prev => ({
        ...prev,
        skippedSteps: [...prev.skippedSteps, currentStep.id]
      }));
      nextStep();
    }
  }, [currentStep, nextStep]);

  // 关闭教程
  const closeTutorial = useCallback(() => {
    setTutorialState(prev => ({
      ...prev,
      isActive: false,
      endTime: new Date()
    }));
  }, []);

  // 重启教程
  const restartTutorial = useCallback(() => {
    setTutorialState({
      isActive: true,
      currentStepIndex: 0,
      completedSteps: [],
      skippedSteps: [],
      startTime: new Date(),
      endTime: undefined
    });
  }, []);

  // 下载图片功能
  const handleDownload = useCallback(async () => {
    if (!phonePreviewRef.current) {
      toast.error('预览区域未找到');
      return;
    }

    try {
      // 这里可以集成实际的下载功能
      // 暂时显示演示提示
      toast.success('演示模式：图片下载功能已触发');
    } catch (error) {
      console.error('下载失败:', error);
      toast.error('下载失败，请重试');
    }
  }, [toast]);

  // 键盘导航
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!tutorialState.isActive) return;

      switch (event.key) {
        case 'ArrowRight':
        case 'Enter':
          event.preventDefault();
          nextStep();
          break;
        case 'ArrowLeft':
          event.preventDefault();
          prevStep();
          break;
        case 'Escape':
          event.preventDefault();
          closeTutorial();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [tutorialState.isActive, nextStep, prevStep, closeTutorial]);

  // 应用演示数据
  useEffect(() => {
    if (currentStep?.demoData && tutorialState.isActive) {
      const { demoData } = currentStep;
      
      if (demoData.recipientName) {
        setRecipientName(demoData.recipientName);
      }
      
      if (demoData.content && demoData.actor) {
        const newMessage: Message = {
          id: Date.now().toString(),
          sender: demoData.actor === 'recipient' ? 'recipient' : 'user',
          content: demoData.content,
          timestamp: new Date().toLocaleTimeString(),
          status: 'default',
          method: 'data'
        };
        setMessages(prev => [...prev, newMessage]);
      }
      
      if (demoData.time) {
        setDeviceTime(demoData.time);
      }
      
      if (demoData.format) {
        setTimeFormat(demoData.format);
      }
      
      if (demoData.batteryPercentage) {
        setBatteryPercentage(demoData.batteryPercentage);
      }
    }
  }, [currentStep, tutorialState.isActive, setDeviceTime, setTimeFormat]);

  return (
    <div ref={pageRef} className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      {/* 教程页面内容 */}
      <main className="pt-16">
        {/* 页面头部 */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="text-center">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Interactive Tutorial
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-4">
                Learn how to create realistic iPhone text message conversations with step-by-step guidance
              </p>
              
              {tutorialState.isActive ? (
                <div className="flex items-center justify-center space-x-2 text-blue-600">
                  <svg className="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <span className="font-medium">Tutorial in progress...</span>
                </div>
              ) : tutorialState.endTime ? (
                <div className="flex flex-col items-center space-y-3">
                  <div className="flex items-center space-x-2 text-green-600">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="font-medium">Tutorial completed!</span>
                  </div>
                  <button
                    onClick={restartTutorial}
                    className="inline-flex items-center px-6 py-2 bg-blue-500 text-white font-medium rounded-lg hover:bg-blue-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Restart Tutorial
                  </button>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-2 text-gray-500">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>Tutorial will start automatically in a moment...</span>
                </div>
              )}
              {false && (
                <button
                  onClick={startTutorial}
                  className="inline-flex items-center px-8 py-3 bg-blue-500 text-white font-medium rounded-lg hover:bg-blue-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  Start Tutorial
                  <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m6-10V7a3 3 0 11-6 0V4a3 3 0 016 0v3zm-6 0V7a3 3 0 11-6 0V4a3 3 0 016 0v3z" />
                  </svg>
                </button>
              )}
            </div>
          </div>
        </div>

        {/* 教程演示区域 - 完全复制主页面布局 */}
        <main
          id="main-content"
          className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-8"
          role="main"
          aria-label="Fake text message generator tutorial"
        >
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
            {/* Left side: Control panel */}
            <div
              className="space-y-3 sm:space-y-4"
              role="region"
              aria-label="Message configuration controls"
            >
              {/* User settings combination - avatar and name */}
              <section
                className="bg-white rounded-lg p-3 sm:p-4 shadow-sm"
                aria-labelledby="user-settings-heading"
              >
                <h3
                  id="user-settings-heading"
                  className="text-base sm:text-lg font-semibold text-gray-900 mb-3"
                >
                  User Settings
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  {/* Avatar upload */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Recipient Avatar</label>
                    <div className="flex items-center space-x-3">
                      <div className="relative group">
                        <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-gray-200 transition-all duration-200 group-hover:border-blue-300">
                          {recipientAvatar ? (
                            <img src={recipientAvatar} alt="Avatar" className="w-full h-full object-cover" />
                          ) : (
                            <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                              <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                              </svg>
                            </div>
                          )}
                        </div>
                        {recipientAvatar && (
                          <button
                            onClick={() => setRecipientAvatar(null)}
                            className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
                          >
                            ×
                          </button>
                        )}
                      </div>
                      <div className="flex-1">
                        <button
                          data-tutorial="avatar-upload"
                          className="w-full px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-all duration-200 font-medium text-sm"
                        >
                          {recipientAvatar ? 'Change' : 'Upload'}
                        </button>
                        <p className="text-xs text-gray-500 mt-1">JPG, PNG up to 5MB</p>
                      </div>
                    </div>
                  </div>

                  {/* Recipient name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Recipient Name</label>
                    <input
                      type="text"
                      value={recipientName}
                      onChange={(e) => setRecipientName(e.target.value)}
                      data-tutorial="recipient-name"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black"
                      placeholder="e.g. fake text message"
                    />
                  </div>
                </div>
              </section>

              {/* Device configuration */}
              <div className="bg-white rounded-lg p-3 sm:p-4 shadow-sm">
                <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-3">Device Configuration</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Device Time</label>
                    <input
                      type="text"
                      value={deviceTime}
                      onChange={(e) => setDeviceTime(e.target.value)}
                      data-tutorial="time-input"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black"
                      placeholder={timeFormat === '12' ? 'H:MM' : 'HH:MM'}
                      maxLength={timeFormat === '12' ? 4 : 5}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Format: {timeFormat === '12' ? '12-hour (e.g. 9:41)' : '24-hour (e.g. 09:41 or 21:30)'}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Time Format</label>
                    <select
                      value={timeFormat}
                      onChange={(e) => setTimeFormat(e.target.value as TimeFormat)}
                      data-tutorial="time-format"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black"
                    >
                      <option value="12">12-hour</option>
                      <option value="24">24-hour</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Theme Mode</label>
                    <select
                      value={mode}
                      onChange={(e) => setMode(e.target.value as ThemeMode)}
                      data-tutorial="theme-toggle"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black"
                    >
                      <option value="light">Light</option>
                      <option value="dark">Dark</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Battery Level</label>
                    <input
                      type="text"
                      value={batteryPercentage}
                      onChange={(e) => setBatteryPercentage(Number(e.target.value) || 0)}
                      data-tutorial="battery-slider"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black"
                      placeholder="85"
                      maxLength={3}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Enter percentage (0-100)
                    </p>
                  </div>
                </div>
              </div>

              {/* Message management */}
              <div className="bg-white rounded-lg p-3 sm:p-4 shadow-sm">
                <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-3">Message Management</h3>
                <div data-tutorial="message-input">
                  <MessageInput
                    onAddUser={() => {
                      const newMessage: Message = {
                        id: Date.now().toString(),
                        sender: 'user',
                        content: 'New sent message',
                        timestamp: new Date().toLocaleTimeString(),
                        status: 'default',
                        method: 'data'
                      };
                      setMessages(prev => [...prev, newMessage]);
                    }}
                    onAddRecipient={() => {
                      const newMessage: Message = {
                        id: Date.now().toString(),
                        sender: 'recipient',
                        content: 'New received message',
                        timestamp: new Date().toLocaleTimeString(),
                        status: 'default',
                        method: 'data'
                      };
                      setMessages(prev => [...prev, newMessage]);
                    }}
                    messages={messages}
                    onMessagesChange={setMessages}
                    placeholder="Type your message here..."
                    maxLength={500}
                  />
                </div>
              </div>
            </div>

            {/* 右侧：iPhone预览 */}
            <div
              className="flex flex-col items-center w-full order-first xl:order-last"
              role="region"
              aria-label="iPhone preview and download controls"
            >
              {/* 操作按钮 */}
              <div className="w-full max-w-sm mb-4 sm:mb-6 px-2 sm:px-4 lg:px-0">
                <div className="flex flex-col xs:flex-row space-y-2 xs:space-y-0 xs:space-x-3">
                  <button
                    onClick={() => {
                      setRecipientName('fake text message');
                      setRecipientAvatar(null);
                      setMessages([]);
                      setMode('light');
                      setBatteryPercentage(100);
                      setDeviceTime('9:41');
                      setTimeFormat('12');
                      toast.success('All settings have been reset');
                    }}
                    className="flex-1 px-3 sm:px-4 py-2 sm:py-2.5 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all duration-200 font-medium text-xs sm:text-sm flex items-center justify-center"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Reset All
                  </button>
                  <button
                    onClick={handleDownload}
                    data-tutorial="download-button"
                    className="flex-1 px-3 sm:px-4 py-2 sm:py-2.5 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-all duration-200 font-medium text-xs sm:text-sm flex items-center justify-center"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Download Image
                  </button>
                </div>
              </div>

              {/* iPhone预览 */}
              <div
                className="xl:sticky xl:top-24"
                role="img"
                aria-label="iPhone message preview"
              >
                <div data-tutorial="phone-preview" ref={phonePreviewRef}>
                  <PhonePreview
                    recipientName={recipientName}
                    recipientAvatar={recipientAvatar}
                    messages={messages}
                    deviceTime={deviceTime}
                    timeFormat={timeFormat}
                    mode={mode}
                    batteryPercentage={batteryPercentage}
                  />
                </div>
                <p className="text-center text-sm text-gray-500 mt-4 font-medium">
                  <svg className="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Preview is scrollable
                </p>
              </div>
            </div>
          </div>
        </main>
      </main>

      <Footer />

      {/* 教程遮罩层 */}
      <TutorialOverlay
        isActive={tutorialState.isActive}
        currentStep={currentStep}
        currentStepIndex={tutorialState.currentStepIndex}
        totalSteps={tutorialSteps.length}
        onNext={nextStep}
        onPrev={prevStep}
        onSkip={skipStep}
        onClose={closeTutorial}
      />
    </div>
  );
}
