'use client';

import React, { useEffect, useState, useCallback, useRef } from 'react';
import { createPortal } from 'react-dom';
import { TutorialStep } from '../../types/message';
import TutorialTooltip from './TutorialTooltip';

interface TutorialOverlayProps {
  isActive: boolean;
  currentStep: TutorialStep | null;
  currentStepIndex: number;
  totalSteps: number;
  onNext: () => void;
  onPrev: () => void;
  onSkip: () => void;
  onClose: () => void;
}

interface ElementPosition {
  top: number;
  left: number;
  width: number;
  height: number;
}

/**
 * 遮罩引导主组件
 * 提供半透明遮罩、元素高亮和聚光灯效果
 */
const TutorialOverlay: React.FC<TutorialOverlayProps> = ({
  isActive,
  currentStep,
  currentStepIndex,
  totalSteps,
  onNext,
  onPrev,
  onSkip,
  onClose
}) => {
  const [targetPosition, setTargetPosition] = useState<ElementPosition | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const overlayRef = useRef<HTMLDivElement>(null);
  const animationFrameRef = useRef<number | undefined>(undefined);

  // 计算目标元素位置
  const calculateTargetPosition = useCallback((selector: string): ElementPosition | null => {
    try {
      const element = document.querySelector(selector);
      if (!element) {
        console.warn(`Tutorial target element not found: ${selector}`);
        return null;
      }

      const rect = element.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

      return {
        top: rect.top + scrollTop,
        left: rect.left + scrollLeft,
        width: rect.width,
        height: rect.height
      };
    } catch (error) {
      console.error('Error calculating target position:', error);
      return null;
    }
  }, []);

  // 滚动到目标元素
  const scrollToTarget = useCallback((selector: string) => {
    try {
      const element = document.querySelector(selector);
      if (!element) return;

      const rect = element.getBoundingClientRect();
      const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
      const viewportWidth = window.innerWidth || document.documentElement.clientWidth;

      // 检查元素是否在视口中，并且有足够的空间显示提示框
      const isFullyVisible = (
        rect.top >= 80 && // 顶部留出适中空间
        rect.left >= 0 &&
        rect.bottom <= viewportHeight - 80 && // 底部留出适中空间
        rect.right <= viewportWidth
      );

      // 如果元素不完全可见，进行最小化滚动
      if (!isFullyVisible) {
        let scrollTop = window.pageYOffset;

        // 如果元素在视口上方，向下滚动一点点
        if (rect.top < 80) {
          const htmlElement = element as HTMLElement;
          scrollTop = htmlElement.offsetTop - 120; // 只滚动到元素上方120px
        }
        // 如果元素在视口下方，向上滚动一点点
        else if (rect.bottom > viewportHeight - 80) {
          const htmlElement = element as HTMLElement;
          scrollTop = htmlElement.offsetTop - (viewportHeight - htmlElement.offsetHeight - 120);
        }

        window.scrollTo({
          top: Math.max(0, scrollTop),
          behavior: 'smooth'
        });

        // 等待滚动完成后更新位置
        setTimeout(() => {
          if (currentStep?.targetSelector) {
            const position = calculateTargetPosition(currentStep.targetSelector);
            setTargetPosition(position);
          }
        }, 600); // 增加等待时间确保滚动完成
      }
    } catch (error) {
      console.error('Error scrolling to target:', error);
    }
  }, [currentStep, calculateTargetPosition]);

  // 更新目标位置
  const updateTargetPosition = useCallback(() => {
    if (currentStep?.targetSelector) {
      const position = calculateTargetPosition(currentStep.targetSelector);
      setTargetPosition(position);
    } else {
      setTargetPosition(null);
    }
  }, [currentStep, calculateTargetPosition]);

  // 处理窗口大小变化
  const handleResize = useCallback(() => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    animationFrameRef.current = requestAnimationFrame(updateTargetPosition);
  }, [updateTargetPosition]);

  // 处理遮罩点击
  const handleOverlayClick = useCallback((event: React.MouseEvent) => {
    // 如果点击的是遮罩本身（不是提示框），则关闭教程
    if (event.target === event.currentTarget) {
      onClose();
    }
  }, [onClose]);

  // 监听步骤变化和窗口大小变化
  useEffect(() => {
    if (isActive && currentStep) {
      // 如果有目标选择器，先滚动到目标位置
      if (currentStep.targetSelector) {
        scrollToTarget(currentStep.targetSelector);
      }

      // 延迟更新位置，确保滚动完成
      const timer = setTimeout(() => {
        updateTargetPosition();
      }, 100);

      window.addEventListener('resize', handleResize);
      window.addEventListener('scroll', handleResize);

      return () => {
        clearTimeout(timer);
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('scroll', handleResize);
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }
      };
    }
  }, [isActive, currentStep, updateTargetPosition, handleResize, scrollToTarget]);

  // 处理显示动画
  useEffect(() => {
    if (isActive) {
      // 延迟显示以确保DOM更新完成
      const timer = setTimeout(() => {
        setIsVisible(true);
        // 显示后再次更新位置，确保准确性
        if (currentStep?.targetSelector) {
          setTimeout(() => {
            const position = calculateTargetPosition(currentStep.targetSelector!);
            setTargetPosition(position);
          }, 100);
        }
      }, 50);
      return () => clearTimeout(timer);
    } else {
      setIsVisible(false);
    }
  }, [isActive, currentStep, calculateTargetPosition]);

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isActive) return;

      switch (event.key) {
        case 'Escape':
          event.preventDefault();
          onClose();
          break;
      }
    };

    if (isActive) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isActive, onClose]);

  // 生成聚光灯效果的CSS
  const getSpotlightStyle = useCallback((): React.CSSProperties => {
    if (!targetPosition) {
      return {
        clipPath: 'inset(0 0 0 0)'
      };
    }

    const { top, left, width, height } = targetPosition;
    const padding = 8; // 高亮区域的内边距
    
    // 使用clip-path创建聚光灯效果
    const clipPath = `polygon(
      0% 0%, 
      0% 100%, 
      ${left - padding}px 100%, 
      ${left - padding}px ${top - padding}px, 
      ${left + width + padding}px ${top - padding}px, 
      ${left + width + padding}px ${top + height + padding}px, 
      ${left - padding}px ${top + height + padding}px, 
      ${left - padding}px 100%, 
      100% 100%, 
      100% 0%
    )`;

    return {
      clipPath
    };
  }, [targetPosition]);

  // 计算提示框位置
  const getTooltipPosition = useCallback(() => {
    if (!targetPosition || !currentStep) {
      return { top: '50%', left: '50%', transform: 'translate(-50%, -50%)' };
    }

    const { top, left, width, height } = targetPosition;
    const tooltipOffset = 20;

    switch (currentStep.position) {
      case 'top':
        return {
          top: `${top - tooltipOffset}px`,
          left: `${left + width / 2}px`,
          transform: 'translate(-50%, -100%)'
        };
      case 'bottom':
        return {
          top: `${top + height + tooltipOffset}px`,
          left: `${left + width / 2}px`,
          transform: 'translate(-50%, 0)'
        };
      case 'left':
        return {
          top: `${top + height / 2}px`,
          left: `${left - tooltipOffset}px`,
          transform: 'translate(-100%, -50%)'
        };
      case 'right':
        return {
          top: `${top + height / 2}px`,
          left: `${left + width + tooltipOffset}px`,
          transform: 'translate(0, -50%)'
        };
      case 'center':
      default:
        return {
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)'
        };
    }
  }, [targetPosition, currentStep]);

  if (!isActive || !currentStep) {
    return null;
  }

  const content = (
    <div
      ref={overlayRef}
      className={`fixed inset-0 z-[9999] transition-all duration-300 ease-out ${
        isVisible ? 'opacity-100' : 'opacity-0'
      }`}
      onClick={handleOverlayClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="tutorial-title"
      aria-describedby="tutorial-description"
    >
      {/* 遮罩层 */}
      <div
        className="absolute inset-0 bg-black/60 transition-all duration-300"
        style={getSpotlightStyle()}
      />
      
      {/* 高亮边框效果 */}
      {targetPosition && (
        <div
          className="absolute border-2 border-blue-500 rounded-lg pointer-events-none transition-all duration-300"
          style={{
            top: `${targetPosition.top - 4}px`,
            left: `${targetPosition.left - 4}px`,
            width: `${targetPosition.width + 8}px`,
            height: `${targetPosition.height + 8}px`,
            boxShadow: '0 0 0 4px rgba(59, 130, 246, 0.3)'
          }}
        />
      )}

      {/* 提示框 */}
      <div
        className="absolute z-10"
        style={getTooltipPosition()}
      >
        <TutorialTooltip
          step={currentStep}
          currentStepIndex={currentStepIndex}
          totalSteps={totalSteps}
          onNext={onNext}
          onPrev={onPrev}
          onSkip={onSkip}
          onClose={onClose}
        />
      </div>
    </div>
  );

  // 确保在客户端环境下才使用 createPortal
  if (typeof document !== 'undefined') {
    return createPortal(content, document.body);
  }

  return null;
};

export default TutorialOverlay;
