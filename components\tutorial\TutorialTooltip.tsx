'use client';

import React from 'react';
import { TutorialStep } from '../../types/message';

interface TutorialTooltipProps {
  step: TutorialStep;
  currentStepIndex: number;
  totalSteps: number;
  onNext: () => void;
  onPrev: () => void;
  onSkip: () => void;
  onClose: () => void;
}

/**
 * 教程提示框组件
 * 显示教程步骤内容、导航按钮和进度指示器
 * 采用Apple风格设计和平滑动画效果
 */
const TutorialTooltip: React.FC<TutorialTooltipProps> = ({
  step,
  currentStepIndex,
  totalSteps,
  onNext,
  onPrev,
  onSkip,
  onClose
}) => {
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === totalSteps - 1;
  const progressPercentage = ((currentStepIndex + 1) / totalSteps) * 100;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 max-w-sm w-full mx-4 transform transition-all duration-300 ease-out">
      {/* 头部区域 */}
      <div className="p-6 pb-4">
        {/* 关闭按钮 */}
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1">
            <h3 
              id="tutorial-title"
              className="text-lg font-semibold text-gray-900 dark:text-white mb-2"
            >
              {step.title}
            </h3>
            <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
              <span>步骤 {currentStepIndex + 1} / {totalSteps}</span>
            </div>
          </div>
          <button
            onClick={onClose}
            className="ml-4 p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
            aria-label="关闭教程"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 进度条 */}
        <div className="mb-4">
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-blue-500 h-2 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>

        {/* 步骤内容 */}
        <div 
          id="tutorial-description"
          className="text-gray-700 dark:text-gray-300 leading-relaxed mb-6"
        >
          <p className="mb-3">{step.description}</p>
          {step.content && (
            <div className="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
              {step.content}
            </div>
          )}
        </div>
      </div>

      {/* 底部导航区域 */}
      <div className="px-6 pb-6">
        <div className="flex items-center justify-between">
          {/* 左侧按钮组 */}
          <div className="flex items-center space-x-2">
            {!isFirstStep && (
              <button
                onClick={onPrev}
                className="px-4 py-2 text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                上一步
              </button>
            )}
            {step.isSkippable && (
              <button
                onClick={onSkip}
                className="px-4 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors duration-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                跳过
              </button>
            )}
          </div>

          {/* 右侧主要按钮 */}
          <div className="flex items-center space-x-2">
            {isLastStep ? (
              <button
                onClick={onClose}
                className="px-6 py-2 bg-green-500 hover:bg-green-600 text-white font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
              >
                完成教程
              </button>
            ) : (
              <button
                onClick={onNext}
                className="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center"
              >
                下一步
                <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 指示箭头（根据位置动态显示） */}
      {step.targetSelector && (
        <div className="absolute pointer-events-none">
          {/* 这里可以根据position属性添加不同方向的箭头 */}
          {step.position === 'top' && (
            <div className="absolute top-full left-1/2 transform -translate-x-1/2">
              <div className="w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-white dark:border-t-gray-800"></div>
            </div>
          )}
          {step.position === 'bottom' && (
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2">
              <div className="w-0 h-0 border-l-8 border-r-8 border-b-8 border-l-transparent border-r-transparent border-b-white dark:border-b-gray-800"></div>
            </div>
          )}
          {step.position === 'left' && (
            <div className="absolute left-full top-1/2 transform -translate-y-1/2">
              <div className="w-0 h-0 border-t-8 border-b-8 border-l-8 border-t-transparent border-b-transparent border-l-white dark:border-l-gray-800"></div>
            </div>
          )}
          {step.position === 'right' && (
            <div className="absolute right-full top-1/2 transform -translate-y-1/2">
              <div className="w-0 h-0 border-t-8 border-b-8 border-r-8 border-t-transparent border-b-transparent border-r-white dark:border-r-gray-800"></div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TutorialTooltip;
