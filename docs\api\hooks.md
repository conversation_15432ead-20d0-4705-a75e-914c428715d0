# 自定义 Hooks API 文档

## 概述

本文档详细说明了假短信生成器项目中的所有自定义 React Hooks，包括时间管理、通知系统等功能性 Hooks。

## useTimeInput Hook

时间输入状态管理 Hook，提供完整的时间输入、验证和格式化功能。

### 基本用法

```typescript
import { useTimeInput } from '../hooks/useTimeInput';

const MyComponent = () => {
  const {
    deviceTime,
    timeFormat,
    timeError,
    isTimeValid,
    handleFormattedTimeChange,
    handleTimeFormatChange,
    resetTime,
    setDeviceTime,
    setTimeFormat
  } = useTimeInput('9:41', '12');

  return (
    <div>
      <input 
        value={deviceTime}
        onChange={(e) => handleFormattedTimeChange(e.target.value)}
        placeholder="输入时间"
      />
      {timeError && <span className="error">{timeError}</span>}
    </div>
  );
};
```

### API 接口

```typescript
function useTimeInput(
  initialTime?: string,      // 初始时间，默认 '9:41'
  initialFormat?: TimeFormat // 初始格式，默认 '12'
): UseTimeInputReturn
```

### 返回值

```typescript
interface UseTimeInputReturn {
  // 状态值
  deviceTime: string;        // 当前设备时间
  timeFormat: TimeFormat;    // 时间格式（'12' | '24'）
  timeError: string;         // 时间错误信息
  isTimeValid: boolean;      // 时间是否有效

  // 处理函数
  handleFormattedTimeChange: (value: string) => void;     // 处理时间输入变更
  handleTimeFormatChange: (format: TimeFormat) => void;   // 处理格式变更
  resetTime: () => void;                                  // 重置时间
  setDeviceTime: (time: string) => void;                  // 直接设置时间
  setTimeFormat: (format: TimeFormat) => void;            // 直接设置格式
}
```

### 详细功能

#### 时间输入处理

```typescript
// 自动格式化输入
handleFormattedTimeChange('941');   // 自动格式化为 '9:41'
handleFormattedTimeChange('1330');  // 24小时制下格式化为 '13:30'

// 边界值验证
handleFormattedTimeChange('25');    // 无效小时，不更新状态
handleFormattedTimeChange('1260');  // 无效分钟，不更新状态
```

#### 格式切换

```typescript
// 切换到24小时制
handleTimeFormatChange('24');

// 切换到12小时制
handleTimeFormatChange('12');

// 格式切换时自动验证当前时间
```

#### 验证机制

```typescript
// 实时验证
const validation = validateTimeFormat(deviceTime, timeFormat);
setIsTimeValid(validation.isValid);
setTimeError(validation.error);

// 验证规则
// 12小时制: 1-12:00-59 (可选 AM/PM)
// 24小时制: 00-23:00-59
```

### 使用示例

#### 基础时间输入

```typescript
const TimeInput = () => {
  const {
    deviceTime,
    timeError,
    isTimeValid,
    handleFormattedTimeChange
  } = useTimeInput();

  return (
    <div className="time-input">
      <input
        type="text"
        value={deviceTime}
        onChange={(e) => handleFormattedTimeChange(e.target.value)}
        className={`input ${!isTimeValid ? 'error' : ''}`}
        placeholder="9:41"
        maxLength={5}
      />
      {timeError && (
        <div className="error-message">{timeError}</div>
      )}
    </div>
  );
};
```

#### 时间格式切换器

```typescript
const TimeFormatToggle = () => {
  const {
    timeFormat,
    handleTimeFormatChange
  } = useTimeInput();

  return (
    <div className="format-toggle">
      <button
        onClick={() => handleTimeFormatChange('12')}
        className={timeFormat === '12' ? 'active' : ''}
      >
        12小时制
      </button>
      <button
        onClick={() => handleTimeFormatChange('24')}
        className={timeFormat === '24' ? 'active' : ''}
      >
        24小时制
      </button>
    </div>
  );
};
```

#### 完整时间控制器

```typescript
const TimeController = () => {
  const timeInput = useTimeInput('9:41', '12');

  return (
    <div className="time-controller">
      <TimeInput {...timeInput} />
      <TimeFormatToggle {...timeInput} />
      
      <div className="actions">
        <button onClick={timeInput.resetTime}>
          重置时间
        </button>
        <button onClick={() => timeInput.setDeviceTime('12:00')}>
          设置为正午
        </button>
      </div>
      
      <div className="status">
        <span>当前时间: {timeInput.deviceTime}</span>
        <span>格式: {timeInput.timeFormat}小时制</span>
        <span>状态: {timeInput.isTimeValid ? '有效' : '无效'}</span>
      </div>
    </div>
  );
};
```

## useNotification Hook

通知系统管理 Hook，提供 Toast 通知和模态对话框功能。

### 基本用法

```typescript
import { useNotification } from '../components/ui/notification';

const MyComponent = () => {
  const notification = useNotification();

  const handleSuccess = () => {
    notification.toast.success('操作成功！');
  };

  const handleConfirm = async () => {
    const confirmed = await notification.modal.confirm({
      title: '确认操作',
      message: '您确定要执行此操作吗？',
      confirmText: '确认',
      cancelText: '取消'
    });

    if (confirmed) {
      console.log('用户确认了操作');
    }
  };

  return (
    <div>
      <button onClick={handleSuccess}>显示成功提示</button>
      <button onClick={handleConfirm}>显示确认对话框</button>
    </div>
  );
};
```

### API 接口

```typescript
function useNotification(): UseNotificationReturn
```

### 返回值

```typescript
interface UseNotificationReturn {
  // Toast 通知方法
  toast: {
    success: (message: string, options?: Partial<SimpleToastOptions>) => string;
    error: (message: string, options?: Partial<SimpleToastOptions>) => string;
    warning: (message: string, options?: Partial<SimpleToastOptions>) => string;
    info: (message: string, options?: Partial<SimpleToastOptions>) => string;
    loading: (message: string, options?: Partial<SimpleToastOptions>) => string;
  };

  // 模态对话框方法
  modal: {
    confirm: (options: ConfirmOptions) => Promise<boolean>;
    alert: (options: AlertOptions) => Promise<void>;
  };

  // 工具方法
  utils: {
    hide: (id: string) => void;                    // 隐藏指定通知
    hideAll: () => void;                           // 隐藏所有通知
    getNotifications: () => NotificationConfig[];  // 获取当前通知列表
    hasNotifications: () => boolean;               // 是否有通知
    hasModals: () => boolean;                      // 是否有模态框
    hasToasts: () => boolean;                      // 是否有Toast
  };
}
```

### Toast 通知

#### 基础 Toast

```typescript
// 成功提示
const successId = notification.toast.success('操作成功！');

// 错误提示
const errorId = notification.toast.error('操作失败，请重试');

// 警告提示
const warningId = notification.toast.warning('请注意数据安全');

// 信息提示
const infoId = notification.toast.info('新功能已上线');

// 加载提示
const loadingId = notification.toast.loading('正在处理中...');
```

#### 高级 Toast 配置

```typescript
// 自定义持续时间
notification.toast.success('操作成功！', {
  duration: 5000,  // 5秒后自动关闭
  position: 'top-right'
});

// 不自动关闭
notification.toast.error('严重错误', {
  duration: 0,  // 不自动关闭
  position: 'bottom-center'
});

// 自定义位置
notification.toast.info('提示信息', {
  position: 'bottom-right'
});
```

### 模态对话框

#### 确认对话框

```typescript
// 基础确认对话框
const confirmed = await notification.modal.confirm({
  title: '删除确认',
  message: '确定要删除这条消息吗？此操作无法撤销。',
  confirmText: '删除',
  cancelText: '取消'
});

if (confirmed) {
  // 用户确认删除
  deleteMessage();
}
```

#### 危险操作确认

```typescript
// 危险操作样式
const confirmed = await notification.modal.confirm({
  title: '危险操作',
  message: '这将永久删除所有数据，无法恢复！',
  confirmText: '确认删除',
  cancelText: '取消',
  variant: 'danger'  // 红色确认按钮
});
```

#### 警告对话框

```typescript
// 信息提示
await notification.modal.alert({
  title: '提示',
  message: '操作已完成',
  status: 'success',
  confirmText: '知道了'
});

// 错误提示
await notification.modal.alert({
  title: '错误',
  message: '网络连接失败，请检查网络设置',
  status: 'error',
  confirmText: '重试'
});

// 警告提示
await notification.modal.alert({
  title: '警告',
  message: '检测到异常操作，请谨慎处理',
  status: 'warning',
  confirmText: '我知道了'
});
```

### 工具方法

#### 通知管理

```typescript
// 隐藏特定通知
const toastId = notification.toast.success('操作成功');
setTimeout(() => {
  notification.utils.hide(toastId);
}, 2000);

// 隐藏所有通知
notification.utils.hideAll();

// 检查通知状态
if (notification.utils.hasModals()) {
  console.log('当前有模态框显示');
}

if (notification.utils.hasToasts()) {
  console.log('当前有Toast通知');
}
```

#### 批量操作

```typescript
// 显示多个通知
const notifications = [
  notification.toast.info('开始处理...'),
  notification.toast.loading('正在上传文件...'),
  notification.toast.warning('请保持网络连接')
];

// 批量隐藏
setTimeout(() => {
  notifications.forEach(id => notification.utils.hide(id));
}, 5000);
```

### 使用场景

#### 表单提交

```typescript
const handleSubmit = async (formData: FormData) => {
  const loadingId = notification.toast.loading('正在提交...');
  
  try {
    await submitForm(formData);
    notification.utils.hide(loadingId);
    notification.toast.success('提交成功！');
  } catch (error) {
    notification.utils.hide(loadingId);
    notification.toast.error('提交失败，请重试');
  }
};
```

#### 数据删除

```typescript
const handleDelete = async (id: string) => {
  const confirmed = await notification.modal.confirm({
    title: '删除确认',
    message: '确定要删除这条记录吗？',
    variant: 'danger'
  });

  if (confirmed) {
    try {
      await deleteRecord(id);
      notification.toast.success('删除成功');
    } catch (error) {
      notification.toast.error('删除失败');
    }
  }
};
```

#### 文件上传

```typescript
const handleFileUpload = async (file: File) => {
  if (file.size > 10 * 1024 * 1024) {
    await notification.modal.alert({
      title: '文件过大',
      message: '文件大小不能超过10MB',
      status: 'warning'
    });
    return;
  }

  const uploadId = notification.toast.loading('正在上传文件...');
  
  try {
    await uploadFile(file);
    notification.utils.hide(uploadId);
    notification.toast.success('文件上传成功');
  } catch (error) {
    notification.utils.hide(uploadId);
    notification.toast.error('文件上传失败');
  }
};
```

## 性能优化

### Hook 优化

```typescript
// 使用 useCallback 优化函数引用
const handleTimeChange = useCallback((value: string) => {
  handleFormattedTimeChange(value);
}, [handleFormattedTimeChange]);

// 使用 useMemo 优化计算结果
const timeDisplay = useMemo(() => {
  return formatDisplayTime(deviceTime, timeFormat);
}, [deviceTime, timeFormat]);
```

### 内存管理

```typescript
// 组件卸载时清理通知
useEffect(() => {
  return () => {
    notification.utils.hideAll();
  };
}, []);

// 限制同时显示的通知数量
const MAX_NOTIFICATIONS = 5;
useEffect(() => {
  const notifications = notification.utils.getNotifications();
  if (notifications.length > MAX_NOTIFICATIONS) {
    // 隐藏最早的通知
    notification.utils.hide(notifications[0].id);
  }
}, []);
```
