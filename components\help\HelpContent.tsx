'use client';

import Link from "next/link";
import { useState } from "react";

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: 'general' | 'technical' | 'features' | 'legal';
}

const faqData: FAQItem[] = [
  {
    id: '1',
    category: 'general',
    question: 'What is the Fake Text Message Generator?',
    answer: 'Our tool allows you to create realistic-looking text message conversations for legitimate creative purposes like videos, mockups, educational content, and artistic projects. Everything is processed locally in your browser for maximum privacy.'
  },
  {
    id: '2',
    category: 'general',
    question: 'Is this service free to use?',
    answer: 'Yes! Our fake text message generator is completely free to use. There are no hidden fees, subscriptions, or premium features. We believe creative tools should be accessible to everyone.'
  },
  {
    id: '3',
    category: 'technical',
    question: 'Do I need to create an account?',
    answer: 'No account required! You can start creating fake text messages immediately. We don\'t collect personal information or require registration, ensuring your privacy and convenience.'
  },
  {
    id: '4',
    category: 'features',
    question: 'Can I add images to messages?',
    answer: 'Yes! You can upload images to include in your text messages. Images are automatically resized and optimized for the iPhone interface. Supported formats include JPG, PNG, and GIF.'
  },
  {
    id: '5',
    category: 'features',
    question: 'How do I customize the conversation?',
    answer: 'You can customize contact names, message content, timestamps, delivery status, theme (light/dark), and time format (12/24 hour). Use the control panel on the left to make adjustments.'
  },
  {
    id: '6',
    category: 'technical',
    question: 'How do I download my conversation?',
    answer: 'Click the "Download Image" button below the iPhone preview. The conversation will be saved as a high-quality PNG image that you can use in your projects.'
  },
  {
    id: '7',
    category: 'legal',
    question: 'What can I use this tool for?',
    answer: 'Legitimate uses include: creative content for videos/stories, educational projects, app mockups, UI/UX design, entertainment, and personal creative projects. See our Terms of Service for full details.'
  },
  {
    id: '8',
    category: 'legal',
    question: 'What is prohibited?',
    answer: 'Prohibited uses include: fraud, harassment, identity theft, creating fake evidence, scams, cyberbullying, defamation, and any illegal activities. Misuse may result in serious legal consequences.'
  },
  {
    id: '9',
    category: 'technical',
    question: 'Is my data safe and private?',
    answer: 'Absolutely! Everything is processed locally in your browser. We don\'t collect, store, or upload your messages, images, or personal information. Your privacy is our top priority.'
  },
  {
    id: '10',
    category: 'features',
    question: 'Can I create group conversations?',
    answer: 'Currently, our tool focuses on two-person conversations (you and one contact). We\'re considering group chat features for future updates based on user feedback.'
  },
  {
    id: '11',
    category: 'technical',
    question: 'What devices and browsers are supported?',
    answer: 'Our tool works on all modern devices and browsers including Chrome, Firefox, Safari, and Edge. It\'s fully responsive and works great on desktop, tablet, and mobile devices.'
  },
  {
    id: '12',
    category: 'features',
    question: 'How do I add images to messages?',
    answer: 'Click the image icon in the message input area to upload an image. Supported formats include JPEG, PNG, GIF, and WebP (up to 10MB). The image will appear instantly in the preview and can be combined with text.'
  },
  {
    id: '13',
    category: 'features',
    question: 'How do I switch between sender and recipient roles?',
    answer: 'Click the colored dot next to each message input: blue dot = sender (you), gray dot = recipient, green dot = system message. The role change is applied instantly to the preview.'
  },
  {
    id: '14',
    category: 'features',
    question: 'What is real-time preview and how does it work?',
    answer: 'Real-time preview means your changes appear instantly in the iPhone interface without clicking any buttons. As you type, upload images, or change settings, the preview updates immediately - it\'s true WYSIWYG (What You See Is What You Get).'
  },
  {
    id: '15',
    category: 'features',
    question: 'How do I set custom message timestamps?',
    answer: 'Hover over the three-dot menu next to any message and select "Date" to open the intelligent date picker. You can edit year, month, day, hour, and minute fields directly. The picker includes real-time validation and stays open for easy multiple adjustments. All time inputs support both 12/24-hour formats with automatic formatting.'
  },
  {
    id: '16',
    category: 'features',
    question: 'What are message status indicators?',
    answer: 'Message status shows delivery state: Default (no indicator), Delivered (✓), Failed (red exclamation), and Read (blue checkmarks). Access this via the three-dot menu next to sender messages.'
  },
  {
    id: '17',
    category: 'technical',
    question: 'Why does the iPhone interface look so realistic?',
    answer: 'We use authentic iOS 17/18 design standards including Dynamic Island, proper status bar time format (no AM/PM), San Francisco fonts, and CSS pseudo-elements for message bubble tails - just like real iPhone Messages.'
  },
  {
    id: '18',
    category: 'features',
    question: 'How does the advanced time management system work?',
    answer: 'Our time system includes intelligent input masking, 12/24-hour format switching, boundary value validation, and an editable date picker. The device time field automatically formats your input (e.g., typing "941" becomes "9:41"), validates ranges, and prevents invalid times. You can switch between formats instantly with real-time conversion.'
  },
  {
    id: '19',
    category: 'features',
    question: 'How do I adjust the battery level in the iPhone interface?',
    answer: 'Use the "Battery Level" field in the Device Configuration section. Enter any percentage from 0-100. The battery icon will automatically show the correct fill level and display a red warning color when the battery is at 20% or below, just like a real iPhone. The battery level is instantly reflected in the preview.'
  },
  {
    id: '12',
    category: 'features',
    question: 'How realistic do the messages look?',
    answer: 'Very realistic! We\'ve carefully replicated the iPhone Messages interface including Dynamic Island, status bar, message bubbles, timestamps, and delivery indicators to match iOS design.'
  }
];

export default function HelpContent() {
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [openFAQ, setOpenFAQ] = useState<string | null>(null);

  const categories = [
    { id: 'all', name: 'All Topics', count: faqData.length },
    { id: 'general', name: 'General', count: faqData.filter(item => item.category === 'general').length },
    { id: 'features', name: 'Features', count: faqData.filter(item => item.category === 'features').length },
    { id: 'technical', name: 'Technical', count: faqData.filter(item => item.category === 'technical').length },
    { id: 'legal', name: 'Legal', count: faqData.filter(item => item.category === 'legal').length },
  ];

  const filteredFAQ = faqData.filter(item => {
    const matchesCategory = activeCategory === 'all' || item.category === activeCategory;
    const matchesSearch = searchTerm === '' || 
      item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.answer.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const toggleFAQ = (id: string) => {
    setOpenFAQ(openFAQ === id ? null : id);
  };

  return (
    <>
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Help Center
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
          Find answers to common questions, learn how to use our features, and get the most out of your fake text message generator.
        </p>

        {/* Search */}
        <div className="max-w-md mx-auto">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              placeholder="Search help articles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Quick Start Guide */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-6">
          🚀 Quick Start Guide
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
              <span className="text-blue-600 font-bold text-lg">1</span>
            </div>
            <h3 className="text-lg font-semibold text-blue-900 mb-2">Set Up Your Conversation</h3>
            <p className="text-blue-800 text-sm">
              Enter the contact name, customize the time and date, and choose your preferred theme (light or dark mode).
            </p>
          </div>
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mb-4">
              <span className="text-green-600 font-bold text-lg">2</span>
            </div>
            <h3 className="text-lg font-semibold text-green-900 mb-2">Add Messages</h3>
            <p className="text-green-800 text-sm">
              Type your messages, choose sender (you or contact), add images if needed, and arrange the conversation flow.
            </p>
          </div>
          
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
              <span className="text-purple-600 font-bold text-lg">3</span>
            </div>
            <h3 className="text-lg font-semibold text-purple-900 mb-2">Download & Use</h3>
            <p className="text-purple-800 text-sm">
              Preview your conversation in real-time, make final adjustments, then download as a high-quality image.
            </p>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="mb-8">
        <div className="flex flex-wrap gap-2 mb-6">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeCategory === category.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category.name} ({category.count})
            </button>
          ))}
        </div>
      </section>

      {/* FAQ Section */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-6">
          Frequently Asked Questions
        </h2>
        
        {filteredFAQ.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">No questions found matching your search.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredFAQ.map((item) => (
              <div key={item.id} className="border border-gray-200 rounded-lg">
                <button
                  onClick={() => toggleFAQ(item.id)}
                  className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <span className="font-medium text-gray-900">{item.question}</span>
                  <svg
                    className={`w-5 h-5 text-gray-500 transition-transform ${
                      openFAQ === item.id ? 'rotate-180' : ''
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                {openFAQ === item.id && (
                  <div className="px-6 pb-4">
                    <p className="text-gray-700">{item.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </section>

      {/* Troubleshooting */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-6">
          🔧 Troubleshooting
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-yellow-900 mb-3">
              Images Not Loading?
            </h3>
            <ul className="text-yellow-800 text-sm space-y-2">
              <li>• Check that your image file is under 10MB</li>
              <li>• Ensure the format is JPG, PNG, or GIF</li>
              <li>• Try refreshing the page and uploading again</li>
              <li>• Clear your browser cache if issues persist</li>
            </ul>
          </div>
          
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-red-900 mb-3">
              Download Not Working?
            </h3>
            <ul className="text-red-800 text-sm space-y-2">
              <li>• Check your browser's download settings</li>
              <li>• Disable ad blockers temporarily</li>
              <li>• Try a different browser (Chrome, Firefox, Safari)</li>
              <li>• Ensure JavaScript is enabled</li>
            </ul>
          </div>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-3">
              Interface Issues?
            </h3>
            <ul className="text-blue-800 text-sm space-y-2">
              <li>• Try refreshing the page (Ctrl+F5 or Cmd+R)</li>
              <li>• Check if your browser is up to date</li>
              <li>• Disable browser extensions temporarily</li>
              <li>• Try using an incognito/private window</li>
            </ul>
          </div>
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-green-900 mb-3">
              Performance Slow?
            </h3>
            <ul className="text-green-800 text-sm space-y-2">
              <li>• Close other browser tabs to free up memory</li>
              <li>• Reduce the number of messages in conversation</li>
              <li>• Use smaller image files when possible</li>
              <li>• Try using a desktop browser for better performance</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Contact Support */}
      <section className="mb-8">
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            Still Need Help?
          </h2>
          <p className="text-gray-600 mb-6">
            Can't find what you're looking for? Our support team is here to help you get the most out of our tool.
          </p>
          <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <Link 
              href="/contact"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              Contact Support
            </Link>
            
            <Link 
              href="/"
              className="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              Try the Generator
            </Link>
          </div>
        </div>
      </section>
    </>
  );
}
