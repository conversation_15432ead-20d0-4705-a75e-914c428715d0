import type { Metadata } from "next";
import Link from "next/link";
import Header from "../../components/layout/Header";
import Footer from "../../components/layout/Footer";

export const metadata: Metadata = {
  title: "Disclaimer - Fake Text Message Generator",
  description: "Important disclaimer about the proper use of our fake text message generator. Learn about legal responsibilities and prohibited uses.",
  keywords: [
    "disclaimer",
    "legal disclaimer",
    "fake text generator disclaimer",
    "usage warning",
    "legal notice",
    "responsibility"
  ],
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    title: "Disclaimer - Fake Text Message Generator",
    description: "Important disclaimer about the proper use of our fake text message generator. Learn about legal responsibilities and prohibited uses.",
    type: "website",
  },
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://faketextmessage.xyz'}/disclaimer`,
  },
};

export default function DisclaimerPage() {
  const lastUpdated = "January 14, 2025";

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Breadcrumb */}
        <nav className="mb-8" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-2 text-sm text-gray-500">
            <li>
              <Link href="/" className="hover:text-blue-600 transition-colors">
                Home
              </Link>
            </li>
            <li className="flex items-center">
              <svg className="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              <span className="text-gray-900">Disclaimer</span>
            </li>
          </ol>
        </nav>

        {/* Header */}
        <div className="mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Disclaimer
          </h1>
          <p className="text-lg text-gray-600 mb-2">
            Important legal notice about the use of our fake text message generator.
          </p>
          <p className="text-sm text-gray-500">
            Last updated: {lastUpdated}
          </p>
        </div>

        {/* Content */}
        <div className="prose prose-lg max-w-none">
          {/* Critical Warning */}
          <div className="bg-red-50 border-l-4 border-red-400 p-6 mb-8">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h2 className="text-lg font-medium text-red-800 mt-0 mb-2">
                  ⚠️ CRITICAL WARNING
                </h2>
                <p className="text-red-700 mb-0">
                  This tool creates <strong>FAKE</strong> text messages for legitimate creative purposes only. 
                  Using this tool for fraud, deception, harassment, or any illegal activity is strictly prohibited 
                  and may result in serious legal consequences.
                </p>
              </div>
            </div>
          </div>

          {/* Purpose and Intent */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Purpose and Intent
            </h2>
            <p className="text-gray-700 mb-4">
              The Fake Text Message Generator is designed exclusively for:
            </p>
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <ul className="list-disc pl-6 space-y-2 text-green-800">
                <li><strong>Creative Content Creation:</strong> Videos, stories, artistic projects, and entertainment</li>
                <li><strong>Educational Purposes:</strong> Teaching digital literacy, social media awareness, and online safety</li>
                <li><strong>Design and Development:</strong> App mockups, UI/UX prototypes, and interface testing</li>
                <li><strong>Personal Projects:</strong> Non-commercial creative endeavors and personal entertainment</li>
              </ul>
            </div>
          </section>

          {/* Prohibited Uses */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Strictly Prohibited Uses
            </h2>
            <p className="text-gray-700 mb-4">
              The following uses are absolutely forbidden and may result in legal action:
            </p>
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <ul className="list-disc pl-6 space-y-3 text-red-800">
                <li><strong>Fraud and Deception:</strong> Creating fake evidence for legal proceedings, insurance claims, or financial fraud</li>
                <li><strong>Identity Theft:</strong> Impersonating others without consent or creating false identities</li>
                <li><strong>Harassment and Cyberbullying:</strong> Creating fake conversations to harm, intimidate, or harass individuals</li>
                <li><strong>Defamation:</strong> Creating false statements that damage someone's reputation or character</li>
                <li><strong>Scams and Phishing:</strong> Using fake messages to deceive people for financial gain</li>
                <li><strong>Revenge Porn:</strong> Creating fake intimate or compromising conversations</li>
                <li><strong>Misinformation:</strong> Spreading false information, conspiracy theories, or fake news</li>
                <li><strong>Legal Manipulation:</strong> Creating fake evidence for court cases or legal disputes</li>
              </ul>
            </div>
          </section>

          {/* User Responsibility */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              User Responsibility
            </h2>
            <p className="text-gray-700 mb-4">
              By using this service, you acknowledge and agree that:
            </p>
            <ul className="list-disc pl-6 space-y-2 text-gray-700">
              <li>You are solely responsible for how you use any content generated by this tool</li>
              <li>You will not use this service for any illegal, harmful, or deceptive purposes</li>
              <li>You understand that misuse of this tool may result in serious legal consequences</li>
              <li>You will respect the rights, privacy, and dignity of others</li>
              <li>You will comply with all applicable local, state, and federal laws</li>
              <li>You will not hold us liable for any consequences of your use of generated content</li>
            </ul>
          </section>

          {/* Legal Consequences */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Legal Consequences of Misuse
            </h2>
            <p className="text-gray-700 mb-4">
              Misusing this tool may result in serious legal consequences, including but not limited to:
            </p>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
              <ul className="list-disc pl-6 space-y-2 text-yellow-800">
                <li><strong>Criminal Charges:</strong> Fraud, identity theft, harassment, and cyberbullying are criminal offenses</li>
                <li><strong>Civil Lawsuits:</strong> Victims may sue for damages, defamation, and emotional distress</li>
                <li><strong>Financial Penalties:</strong> Fines, restitution, and legal fees can be substantial</li>
                <li><strong>Imprisonment:</strong> Serious offenses may result in jail or prison time</li>
                <li><strong>Permanent Record:</strong> Criminal convictions can affect employment, housing, and other opportunities</li>
              </ul>
            </div>
          </section>

          {/* No Liability */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Limitation of Liability
            </h2>
            <p className="text-gray-700 mb-4">
              We explicitly disclaim all liability for:
            </p>
            <ul className="list-disc pl-6 space-y-2 text-gray-700">
              <li>Any misuse of content generated by our service</li>
              <li>Legal consequences arising from user actions</li>
              <li>Damages caused by user-generated content</li>
              <li>Third-party claims related to user activities</li>
              <li>Any illegal or harmful use of our service</li>
            </ul>
            <p className="text-gray-700 mt-4">
              <strong>You agree to indemnify and hold us harmless</strong> from any claims, damages, or legal actions arising from your use of this service.
            </p>
          </section>

          {/* Detection and Reporting */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Detection and Reporting
            </h2>
            <p className="text-gray-700 mb-4">
              Please be aware that:
            </p>
            <ul className="list-disc pl-6 space-y-2 text-gray-700">
              <li>Fake messages can often be detected through digital forensics</li>
              <li>Screenshots and images contain metadata that can reveal manipulation</li>
              <li>We cooperate with law enforcement when illegal use is suspected</li>
              <li>We encourage reporting of suspected misuse to appropriate authorities</li>
            </ul>
          </section>

          {/* Educational Notice */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Educational Notice
            </h2>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <p className="text-blue-800 mb-4">
                <strong>Digital Literacy Reminder:</strong> In our digital age, it's important to:
              </p>
              <ul className="list-disc pl-6 space-y-2 text-blue-800">
                <li>Always verify the authenticity of digital content</li>
                <li>Be skeptical of screenshots and images shared online</li>
                <li>Understand that digital content can be easily manipulated</li>
                <li>Report suspected fake content to appropriate platforms</li>
                <li>Educate others about the existence of content generation tools</li>
              </ul>
            </div>
          </section>

          {/* Service Modifications */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Service Modifications
            </h2>
            <p className="text-gray-700">
              We reserve the right to modify, suspend, or discontinue this service at any time, 
              especially if we detect widespread misuse or if legal requirements change. 
              We may also implement additional safeguards or restrictions as needed.
            </p>
          </section>

          {/* Reporting Misuse */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Reporting Misuse
            </h2>
            <p className="text-gray-700 mb-4">
              If you become aware of misuse of our service, please report it:
            </p>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
              <ul className="space-y-2 text-gray-700">
                <li><strong>Email:</strong> <EMAIL></li>
                <li><strong>Contact Page:</strong> <Link href="/contact" className="text-blue-600 hover:text-blue-800">faketextmessage.xyz/contact</Link></li>
                <li><strong>Law Enforcement:</strong> Report criminal activity to local authorities</li>
              </ul>
            </div>
          </section>

          {/* Final Warning */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-red-900 mb-2">
              Final Warning
            </h3>
            <p className="text-red-800 mb-0">
              This tool is provided for legitimate creative purposes only. Any misuse is entirely at your own risk and may result in serious legal consequences. 
              <strong> Use responsibly and ethically.</strong>
            </p>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <Link 
              href="/terms-of-service"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              View Terms of Service
            </Link>
            <Link 
              href="/contact"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
            >
              Contact Us
              <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
