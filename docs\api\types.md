# TypeScript 类型定义文档

## 概述

本文档详细说明了假短信生成器项目中使用的所有 TypeScript 类型定义，包括消息类型、组件属性、工具函数等。

## 核心类型

### Message - 消息接口

```typescript
interface Message {
  id: string;                    // 唯一标识符
  sender: 'user' | 'recipient' | 'system';  // 发送者类型
  content: string;               // 消息内容
  timestamp?: string;            // 时间戳（可选）
  status?: 'default' | 'delivered' | 'failed' | 'read';  // 消息状态
  imageUrl?: string;             // 图片URL（可选）
  method?: 'data' | 'image' | 'unknown';  // 消息类型
}
```

**字段说明：**
- `id`: 消息的唯一标识符，通常使用时间戳 + 随机字符串生成
- `sender`: 消息发送者类型，决定消息气泡的样式和位置
- `content`: 消息的文本内容，支持空字符串（纯图片消息）
- `timestamp`: 消息的时间戳，用于显示发送时间
- `status`: 消息的状态，影响消息的视觉反馈
- `imageUrl`: 图片消息的URL，可以是本地blob URL或网络URL
- `method`: 消息的创建方式，用于内部处理逻辑

### DraftMessage - 草稿消息接口

```typescript
interface DraftMessage {
  id: string;                   // 唯一标识符
  actor: 'sender' | 'recipient' | 'system';  // 角色类型
  content: string;              // 消息内容
  imageFile?: File;             // 图片文件对象
  imageUrl?: string;            // 图片预览URL
  timestamp?: string;           // 时间戳
  status?: 'default' | 'delivered' | 'failed' | 'read';  // 消息状态
  method: 'data' | 'image' | 'unknown';  // 消息类型（必需）
}
```

**与 Message 的区别：**
- `actor`: 使用更明确的角色术语
- `imageFile`: 包含原始文件对象，用于上传和处理
- `method`: 必需字段，明确消息的创建方式

## 基础类型

### TimeFormat - 时间格式

```typescript
type TimeFormat = '12' | '24';
```

**说明：**
- `'12'`: 12小时制，显示 AM/PM
- `'24'`: 24小时制，使用 00:00-23:59 格式

### ThemeMode - 主题模式

```typescript
type ThemeMode = 'light' | 'dark';
```

**说明：**
- `'light'`: 浅色主题，白色背景
- `'dark'`: 深色主题，黑色背景

### MessageSender - 消息发送者

```typescript
type MessageSender = 'user' | 'recipient' | 'system';
```

**说明：**
- `'user'`: 用户发送的消息，显示在右侧，蓝色气泡
- `'recipient'`: 接收方的消息，显示在左侧，灰色气泡
- `'system'`: 系统消息，显示在右侧，绿色气泡

## 组件属性类型

### PhonePreviewProps - iPhone预览组件属性

```typescript
interface PhonePreviewProps {
  recipientName: string;         // 联系人姓名
  recipientAvatar: string | null; // 联系人头像URL
  messages: Message[];           // 消息列表
  deviceTime: string;            // 设备时间显示
  timeFormat: TimeFormat;        // 时间格式
  mode: ThemeMode;              // 主题模式
  batteryPercentage?: number;    // 电池电量百分比 (0-100)，默认85%
}
```

**属性说明：**
- `recipientName`: 显示在聊天头部的联系人姓名
- `recipientAvatar`: 联系人头像图片URL，null时显示默认头像
- `messages`: 要显示的消息列表
- `deviceTime`: iPhone状态栏显示的时间
- `timeFormat`: 时间显示格式（12小时制或24小时制）
- `mode`: 界面主题（浅色或深色模式）
- `batteryPercentage`: 电池电量百分比，范围0-100，≤20%时显示红色警告

### MessageBubbleProps - 消息气泡组件属性

```typescript
interface MessageBubbleProps {
  message: Message;              // 消息对象
  recipientName: string;         // 联系人姓名
  recipientAvatar: string | null; // 联系人头像
  mode: ThemeMode;              // 主题模式
  timeFormat: TimeFormat;        // 时间格式
  showTime?: boolean;           // 是否显示时间（可选）
}
```

### BatteryIconProps - 电池图标组件属性

```typescript
interface BatteryIconProps {
  percentage?: number;    // 电量百分比 (0-100)，默认85
  charging?: boolean;     // 是否显示充电状态，默认false
  className?: string;     // 自定义CSS类名
  color?: string;         // 图标颜色，默认'currentColor'
  mode?: 'light' | 'dark'; // 主题模式，默认'light'
}
```

**属性说明：**
- `percentage`: 电池电量百分比，范围0-100，≤20%时自动显示红色警告
- `charging`: 是否显示充电图标叠加
- `className`: 自定义CSS类名，通常用于设置尺寸
- `color`: 电池图标颜色，正常电量时使用此颜色
- `mode`: 主题模式，影响充电图标的颜色显示

## 角色配置类型

### RoleConfig - 角色配置接口

```typescript
interface RoleConfig {
  color: string;                 // 角色指示器颜色
  label: string;                 // 角色显示标签
  mappedSender: MessageSender;   // 映射的发送者类型
}
```

### ROLE_CONFIG - 角色配置常量

```typescript
const ROLE_CONFIG: Record<string, RoleConfig> = {
  recipient: {
    color: 'rgb(209, 209, 211)', // 灰色
    label: 'Recipient',
    mappedSender: 'recipient'
  },
  sender: {
    color: 'rgb(0, 122, 255)',   // 蓝色
    label: 'User/Sender',
    mappedSender: 'user'
  },
  system: {
    color: 'rgb(52, 199, 89)',   // 绿色
    label: 'System/Third Party',
    mappedSender: 'system'
  }
};
```

## 通知系统类型

### NotificationType - 通知类型

```typescript
type NotificationType = 'toast' | 'modal';
```

### NotificationStatus - 通知状态

```typescript
type NotificationStatus = 'loading' | 'success' | 'error' | 'warning' | 'info' | 'confirm';
```

### ConfirmOptions - 确认对话框选项

```typescript
interface ConfirmOptions {
  title?: string;                // 对话框标题
  message: string;               // 对话框消息
  confirmText?: string;          // 确认按钮文本
  cancelText?: string;           // 取消按钮文本
  variant?: 'primary' | 'danger'; // 按钮样式变体
  width?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'; // 对话框宽度
}
```

### AlertOptions - 警告对话框选项

```typescript
interface AlertOptions {
  title?: string;                // 对话框标题
  message: string;               // 对话框消息
  confirmText?: string;          // 确认按钮文本
  status?: 'info' | 'warning' | 'error' | 'success'; // 状态类型
  width?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'; // 对话框宽度
}
```

## 工具函数类型

### 时间验证结果

```typescript
interface TimeValidationResult {
  isValid: boolean;              // 是否有效
  error: string;                 // 错误信息
}
```

### 时间处理函数签名

```typescript
// 格式化显示时间
function formatDisplayTime(time: string, format: TimeFormat): string;

// 验证时间格式
function validateTimeFormat(time: string, format: TimeFormat): TimeValidationResult;

// 格式化时间输入
function formatTimeInput(value: string, format: TimeFormat): string;

// 验证时间输入边界值
function isValidTimeInput(hours: string, minutes: string, format: TimeFormat): boolean;
```

## Hook 类型

### useTimeInput Hook 返回类型

```typescript
interface UseTimeInputReturn {
  deviceTime: string;            // 当前设备时间
  timeFormat: TimeFormat;        // 时间格式
  timeError: string;             // 时间错误信息
  isTimeValid: boolean;          // 时间是否有效
  handleFormattedTimeChange: (value: string) => void; // 处理时间变更
  handleTimeFormatChange: (format: TimeFormat) => void; // 处理格式变更
  resetTime: () => void;         // 重置时间
  setDeviceTime: (time: string) => void; // 设置设备时间
  setTimeFormat: (format: TimeFormat) => void; // 设置时间格式
}
```

### useNotification Hook 返回类型

```typescript
interface UseNotificationReturn {
  toast: {
    success: (message: string, options?: Partial<SimpleToastOptions>) => string;
    error: (message: string, options?: Partial<SimpleToastOptions>) => string;
    warning: (message: string, options?: Partial<SimpleToastOptions>) => string;
    info: (message: string, options?: Partial<SimpleToastOptions>) => string;
    loading: (message: string, options?: Partial<SimpleToastOptions>) => string;
  };
  modal: {
    confirm: (options: ConfirmOptions) => Promise<boolean>;
    alert: (options: AlertOptions) => Promise<void>;
  };
  utils: {
    hide: (id: string) => void;
    hideAll: () => void;
    getNotifications: () => NotificationConfig[];
    hasNotifications: () => boolean;
    hasModals: () => boolean;
    hasToasts: () => boolean;
  };
}
```

## 类型使用示例

### 创建消息

```typescript
import { Message, MessageSender } from '../types/message';

const createMessage = (
  content: string, 
  sender: MessageSender,
  imageUrl?: string
): Message => {
  return {
    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
    sender,
    content,
    timestamp: new Date().toLocaleTimeString(),
    status: 'default',
    imageUrl,
    method: imageUrl ? 'image' : 'data'
  };
};
```

### 类型守卫

```typescript
// 检查是否为有效的消息发送者
function isValidMessageSender(sender: string): sender is MessageSender {
  return ['user', 'recipient', 'system'].includes(sender);
}

// 检查是否为图片消息
function isImageMessage(message: Message): boolean {
  return Boolean(message.imageUrl);
}

// 检查是否为文本消息
function isTextMessage(message: Message): boolean {
  return Boolean(message.content.trim());
}
```

## 类型扩展

### 扩展消息类型

```typescript
// 扩展 Message 接口以支持新功能
interface ExtendedMessage extends Message {
  audioUrl?: string;             // 音频消息
  videoUrl?: string;             // 视频消息
  location?: {                   // 位置消息
    lat: number;
    lng: number;
    address: string;
  };
  reactions?: string[];          // 消息反应
  replyTo?: string;              // 回复的消息ID
}
```

### 自定义主题类型

```typescript
// 扩展主题模式
type ExtendedThemeMode = ThemeMode | 'auto' | 'system';

// 自定义颜色主题
interface CustomTheme {
  mode: ThemeMode;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
  };
}
```
