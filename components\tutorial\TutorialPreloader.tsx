'use client';

import { useEffect } from 'react';

/**
 * 教程预加载器组件
 * 预加载教程相关资源以提升性能
 */
const TutorialPreloader: React.FC = () => {
  useEffect(() => {
    // 预加载教程页面
    const preloadTutorial = () => {
      // 预加载教程页面路由
      if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
        window.requestIdleCallback(() => {
          const link = document.createElement('link');
          link.rel = 'prefetch';
          link.href = '/tutorial';
          document.head.appendChild(link);
        });
      }
    };

    // 延迟预加载以避免影响首屏性能
    const timer = setTimeout(preloadTutorial, 2000);
    
    return () => clearTimeout(timer);
  }, []);

  return null; // 这是一个无UI组件
};

export default TutorialPreloader;
