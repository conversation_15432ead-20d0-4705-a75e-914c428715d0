# 工具函数 API 文档

## 概述

本文档详细说明了假短信生成器项目中的所有工具函数，包括时间处理、下载功能等实用工具。

## 时间处理工具 (utils/time.ts)

### formatDisplayTime

格式化显示时间，支持 12/24 小时制转换。

```typescript
function formatDisplayTime(time: string, format: TimeFormat): string
```

**参数：**
- `time`: 输入时间字符串（如 "9:41", "21:30"）
- `format`: 目标时间格式（"12" 或 "24"）

**返回值：**
- 格式化后的时间字符串

**使用示例：**
```typescript
import { formatDisplayTime } from '../utils/time';

// 12小时制转换（状态栏时间不显示AM/PM）
formatDisplayTime('9:41', '12');    // "9:41"
formatDisplayTime('21:30', '12');   // "9:30"

// 24小时制转换
formatDisplayTime('9:41 AM', '24'); // "09:41"
formatDisplayTime('9:30 PM', '24'); // "21:30"
```

**特殊处理：**
- 自动识别输入格式（12/24小时制）
- 智能补零（9:41 → 09:41 在24小时制下）
- 保持原格式（如果已经是目标格式）

### validateTimeFormat

验证时间格式的有效性。

```typescript
function validateTimeFormat(time: string, format: TimeFormat): {
  isValid: boolean;
  error: string;
}
```

**参数：**
- `time`: 要验证的时间字符串
- `format`: 期望的时间格式

**返回值：**
- `isValid`: 是否为有效格式
- `error`: 错误信息（如果无效）

**使用示例：**
```typescript
import { validateTimeFormat } from '../utils/time';

// 有效时间
validateTimeFormat('9:41', '12');
// { isValid: true, error: '' }

// 无效时间
validateTimeFormat('25:00', '24');
// { isValid: false, error: 'Please enter a valid 24-hour time format (e.g. 09:41 or 21:30)' }

// 空时间
validateTimeFormat('', '12');
// { isValid: false, error: 'Please enter a time' }
```

**验证规则：**
- **12小时制**: 1-12:00-59 (可选 AM/PM)
- **24小时制**: 00-23:00-59
- **格式要求**: 必须包含冒号分隔符

### formatTimeInput

格式化时间输入，提供输入掩码功能。

```typescript
function formatTimeInput(value: string, format: TimeFormat): string
```

**参数：**
- `value`: 用户输入的原始字符串
- `format`: 时间格式

**返回值：**
- 格式化后的输入字符串

**使用示例：**
```typescript
import { formatTimeInput } from '../utils/time';

// 12小时制输入
formatTimeInput('9', '12');     // "9"
formatTimeInput('94', '12');    // "9:4"
formatTimeInput('941', '12');   // "9:41"

// 24小时制输入
formatTimeInput('9', '24');     // "09"
formatTimeInput('094', '24');   // "09:4"
formatTimeInput('0941', '24');  // "09:41"
```

**输入掩码特性：**
- 自动插入冒号分隔符
- 限制输入为数字
- 智能格式化（12小时制允许单数字小时）
- 最大4位数字限制

### isValidTimeInput

验证时间输入的边界值。

```typescript
function isValidTimeInput(hours: string, minutes: string, format: TimeFormat): boolean
```

**参数：**
- `hours`: 小时字符串
- `minutes`: 分钟字符串
- `format`: 时间格式

**返回值：**
- 是否在有效范围内

**使用示例：**
```typescript
import { isValidTimeInput } from '../utils/time';

// 12小时制验证
isValidTimeInput('9', '41', '12');   // true
isValidTimeInput('13', '30', '12');  // false (超出12小时制范围)

// 24小时制验证
isValidTimeInput('21', '30', '24');  // true
isValidTimeInput('25', '00', '24');  // false (超出24小时制范围)

// 分钟验证
isValidTimeInput('10', '60', '12');  // false (分钟超出范围)
```

**验证范围：**
- **12小时制小时**: 1-12
- **24小时制小时**: 0-23
- **分钟**: 0-59

### formatTime

通用时间格式化函数。

```typescript
function formatTime(time: string, targetFormat: TimeFormat): string
```

**参数：**
- `time`: 输入时间字符串
- `targetFormat`: 目标格式

**返回值：**
- 转换后的时间字符串

**使用示例：**
```typescript
import { formatTime } from '../utils/time';

// 通用格式化
formatTime('9:41 AM', '24');  // "09:41"
formatTime('21:30', '12');    // "9:30 PM"
formatTime('', '12');         // "" (空字符串保持不变)
```

## 下载工具 (utils/download.ts)

### downloadPhonePreview

下载 iPhone 预览界面为图片。

```typescript
function downloadPhonePreview(element: HTMLElement, filename?: string): Promise<void>
```

**参数：**
- `element`: 要截图的 DOM 元素
- `filename`: 下载文件名（可选，默认自动生成）

**返回值：**
- Promise，完成时无返回值

**使用示例：**
```typescript
import { downloadPhonePreview } from '../utils/download';

const phoneRef = useRef<HTMLDivElement>(null);

const handleDownload = async () => {
  if (phoneRef.current) {
    try {
      await downloadPhonePreview(phoneRef.current, 'my-fake-message.png');
      console.log('下载成功');
    } catch (error) {
      console.error('下载失败:', error);
    }
  }
};

<PhonePreview ref={phoneRef} {...props} />
<button onClick={handleDownload}>下载图片</button>
```

**功能特性：**
- 使用 html2canvas-pro 进行高质量截图
- 自动生成带时间戳的文件名
- 支持自定义文件名
- 错误处理和异常捕获

**配置选项：**
```typescript
// html2canvas 配置
const options = {
  backgroundColor: null,        // 透明背景
  scale: 2,                    // 2倍分辨率
  useCORS: true,               // 支持跨域图片
  allowTaint: false,           // 防止画布污染
  logging: false,              // 禁用日志
  width: element.offsetWidth,   // 固定宽度
  height: element.offsetHeight  // 固定高度
};
```

## 常用工具函数

### generateId

生成唯一标识符。

```typescript
function generateId(): string
```

**返回值：**
- 唯一的字符串ID

**使用示例：**
```typescript
const messageId = generateId();
// "1703123456789abc123def"
```

**实现原理：**
```typescript
const generateId = () => 
  Date.now().toString() + Math.random().toString(36).substr(2, 9);
```

### debounce

防抖函数，限制函数调用频率。

```typescript
function debounce<T extends (...args: any[]) => any>(
  func: T, 
  wait: number
): (...args: Parameters<T>) => void
```

**参数：**
- `func`: 要防抖的函数
- `wait`: 等待时间（毫秒）

**使用示例：**
```typescript
import { debounce } from '../utils/common';

const debouncedSearch = debounce((query: string) => {
  // 搜索逻辑
  console.log('搜索:', query);
}, 300);

// 连续调用只会执行最后一次
debouncedSearch('a');
debouncedSearch('ab');
debouncedSearch('abc'); // 只有这次会执行
```

### throttle

节流函数，限制函数执行频率。

```typescript
function throttle<T extends (...args: any[]) => any>(
  func: T, 
  limit: number
): (...args: Parameters<T>) => void
```

**参数：**
- `func`: 要节流的函数
- `limit`: 时间间隔（毫秒）

**使用示例：**
```typescript
import { throttle } from '../utils/common';

const throttledScroll = throttle(() => {
  // 滚动处理逻辑
  console.log('滚动事件');
}, 100);

window.addEventListener('scroll', throttledScroll);
```

## 错误处理

### TimeError

时间处理相关错误类。

```typescript
class TimeError extends Error {
  constructor(message: string, public code: string) {
    super(message);
    this.name = 'TimeError';
  }
}
```

**使用示例：**
```typescript
try {
  const result = validateTimeFormat('invalid', '12');
  if (!result.isValid) {
    throw new TimeError(result.error, 'INVALID_FORMAT');
  }
} catch (error) {
  if (error instanceof TimeError) {
    console.error('时间格式错误:', error.message);
  }
}
```

### DownloadError

下载功能相关错误类。

```typescript
class DownloadError extends Error {
  constructor(message: string, public cause?: Error) {
    super(message);
    this.name = 'DownloadError';
  }
}
```

## 性能优化

### 缓存机制

```typescript
// 时间格式化结果缓存
const formatCache = new Map<string, string>();

function cachedFormatTime(time: string, format: TimeFormat): string {
  const key = `${time}-${format}`;
  if (formatCache.has(key)) {
    return formatCache.get(key)!;
  }
  
  const result = formatTime(time, format);
  formatCache.set(key, result);
  return result;
}
```

### 内存管理

```typescript
// 清理缓存
function clearFormatCache(): void {
  formatCache.clear();
}

// 限制缓存大小
const MAX_CACHE_SIZE = 100;
function manageCacheSize(): void {
  if (formatCache.size > MAX_CACHE_SIZE) {
    const firstKey = formatCache.keys().next().value;
    formatCache.delete(firstKey);
  }
}
```

## 测试工具

### 时间格式测试

```typescript
// 测试所有时间格式
const testTimeFormats = () => {
  const testCases = [
    { input: '9:41', format: '12' as TimeFormat, expected: '9:41' },
    { input: '21:30', format: '12' as TimeFormat, expected: '9:30' }, // 状态栏不显示AM/PM
    { input: '9:41 AM', format: '24' as TimeFormat, expected: '09:41' },
  ];

  testCases.forEach(({ input, format, expected }) => {
    const result = formatDisplayTime(input, format);
    console.assert(result === expected, `Expected ${expected}, got ${result}`);
  });
};
```

### 下载功能测试

```typescript
// 模拟下载测试
const testDownload = async () => {
  const mockElement = document.createElement('div');
  mockElement.style.width = '316px';
  mockElement.style.height = '684px';
  
  try {
    await downloadPhonePreview(mockElement, 'test.png');
    console.log('下载测试通过');
  } catch (error) {
    console.error('下载测试失败:', error);
  }
};
```
