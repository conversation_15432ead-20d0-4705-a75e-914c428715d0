import type { Metadata } from "next";
import Link from "next/link";
import Header from "../../components/layout/Header";
import Footer from "../../components/layout/Footer";
import HelpContent from "../../components/help/HelpContent";

export const metadata: Metadata = {
  title: "Help Center - Fake Text Message Generator",
  description: "Learn how to use our iPhone Messages generator: image upload, role switching, real-time WYSIWYG preview, date customization, and professional export features. Complete tutorials and FAQ.",
  keywords: [
    "help",
    "tutorial",
    "FAQ",
    "support",
    "fake text",
    "fake text message iphone",
    "text message generator",
    "iPhone Messages help",
    "image upload tutorial",
    "role switching guide",
    "real-time preview",
    "WYSIWYG editor help",
    "date picker tutorial",
    "message status guide",
    "fake text generator help",
    "iOS Messages tutorial"
  ],
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    title: "Help Center - Fake Text Message Generator",
    description: "Get help with our fake text message generator. Find tutorials, FAQ, troubleshooting guides, and tips for creating realistic text conversations.",
    type: "website",
  },
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://faketextmessage.xyz'}/help`,
  },
};

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: 'general' | 'technical' | 'features' | 'legal';
}

const faqData: FAQItem[] = [
  {
    id: '1',
    category: 'general',
    question: 'What is the Fake Text Message Generator?',
    answer: 'Our tool allows you to create realistic-looking text message conversations for legitimate creative purposes like videos, mockups, educational content, and artistic projects. Everything is processed locally in your browser for maximum privacy.'
  },
  {
    id: '2',
    category: 'general',
    question: 'Is this service free to use?',
    answer: 'Yes! Our fake text message generator is completely free to use. There are no hidden fees, subscriptions, or premium features. We believe creative tools should be accessible to everyone.'
  },
  {
    id: '3',
    category: 'technical',
    question: 'Do I need to create an account?',
    answer: 'No account required! You can start creating fake text messages immediately. We don\'t collect personal information or require registration, ensuring your privacy and convenience.'
  },
  {
    id: '4',
    category: 'features',
    question: 'Can I add images to messages?',
    answer: 'Yes! You can upload images to include in your text messages. Images are automatically resized and optimized for the iPhone interface. Supported formats include JPG, PNG, and GIF.'
  },
  {
    id: '5',
    category: 'features',
    question: 'How do I customize the conversation?',
    answer: 'You can customize contact names, message content, timestamps, delivery status, theme (light/dark), and time format (12/24 hour). Use the control panel on the left to make adjustments.'
  },
  {
    id: '6',
    category: 'technical',
    question: 'How do I download my conversation?',
    answer: 'Click the "Download Image" button below the iPhone preview. The conversation will be saved as a high-quality PNG image that you can use in your projects.'
  },
  {
    id: '7',
    category: 'legal',
    question: 'What can I use this tool for?',
    answer: 'Legitimate uses include: creative content for videos/stories, educational projects, app mockups, UI/UX design, entertainment, and personal creative projects. See our Terms of Service for full details.'
  },
  {
    id: '8',
    category: 'legal',
    question: 'What is prohibited?',
    answer: 'Prohibited uses include: fraud, harassment, identity theft, creating fake evidence, scams, cyberbullying, defamation, and any illegal activities. Misuse may result in serious legal consequences.'
  },
  {
    id: '9',
    category: 'technical',
    question: 'Is my data safe and private?',
    answer: 'Absolutely! Everything is processed locally in your browser. We don\'t collect, store, or upload your messages, images, or personal information. Your privacy is our top priority.'
  },
  {
    id: '10',
    category: 'features',
    question: 'Can I create group conversations?',
    answer: 'Currently, our tool focuses on two-person conversations (you and one contact). We\'re considering group chat features for future updates based on user feedback.'
  },
  {
    id: '11',
    category: 'technical',
    question: 'What devices and browsers are supported?',
    answer: 'Our tool works on all modern devices and browsers including Chrome, Firefox, Safari, and Edge. It\'s fully responsive and works great on desktop, tablet, and mobile devices.'
  },
  {
    id: '12',
    category: 'features',
    question: 'How realistic do the messages look?',
    answer: 'Very realistic! We\'ve carefully replicated the iPhone Messages interface including Dynamic Island, status bar, message bubbles, timestamps, and delivery indicators to match iOS design.'
  }
];

export default function HelpPage() {

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Breadcrumb */}
        <nav className="mb-8" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-2 text-sm text-gray-500">
            <li>
              <Link href="/" className="hover:text-blue-600 transition-colors">
                Home
              </Link>
            </li>
            <li className="flex items-center">
              <svg className="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              <span className="text-gray-900">Help Center</span>
            </li>
          </ol>
        </nav>

        <HelpContent />


      </main>

      <Footer />
    </div>
  );
}
