# 高级时间管理系统

## 概述

假短信生成器项目集成了一套完整的时间管理系统，提供智能时间输入、格式化、验证和自定义功能。该系统包括 `useTimeInput` Hook、时间工具函数库和可编辑的日期选择器组件。

## 核心功能

### 🕐 智能时间输入管理

#### useTimeInput Hook

`useTimeInput` 是项目的核心时间管理 Hook，提供完整的时间状态管理和验证功能。

```typescript
import { useTimeInput } from '../hooks/useTimeInput';

const {
  deviceTime,              // 当前设备时间
  timeFormat,              // 时间格式（'12' | '24'）
  timeError,               // 时间错误信息
  isTimeValid,             // 时间是否有效
  handleFormattedTimeChange, // 处理时间输入变更
  handleTimeFormatChange,   // 处理格式变更
  resetTime,               // 重置时间
  setDeviceTime,           // 直接设置时间
  setTimeFormat            // 直接设置格式
} = useTimeInput('9:41', '12');
```

#### 输入掩码功能

系统提供智能输入掩码，自动格式化用户输入：

```typescript
// 12小时制输入示例
'9' → '9'
'94' → '9:4'
'941' → '9:41'

// 24小时制输入示例
'9' → '09'
'094' → '09:4'
'0941' → '09:41'
```

### ⚡ 实时验证系统

#### 边界值验证

```typescript
// 12小时制验证
isValidTimeInput('9', '41', '12');   // true
isValidTimeInput('13', '30', '12');  // false (超出范围)

// 24小时制验证
isValidTimeInput('21', '30', '24');  // true
isValidTimeInput('25', '00', '24');  // false (超出范围)

// 分钟验证
isValidTimeInput('10', '60', '12');  // false (分钟超出范围)
```

#### 格式验证

```typescript
// 有效时间格式
validateTimeFormat('9:41', '12');
// { isValid: true, error: '' }

// 无效时间格式
validateTimeFormat('25:00', '24');
// { isValid: false, error: 'Please enter a valid 24-hour time format' }
```

### 🔄 格式转换系统

#### 12/24小时制转换

```typescript
// 状态栏时间显示（不显示AM/PM）
formatDisplayTime('9:41 AM', '12');  // "9:41"
formatDisplayTime('21:30', '12');    // "9:30"

// 24小时制显示
formatDisplayTime('9:41 AM', '24');  // "09:41"
formatDisplayTime('9:30 PM', '24');  // "21:30"
```

#### 通用时间格式化

```typescript
// 带AM/PM的格式化
formatTime('9:41', '12');   // "9:41 AM"
formatTime('21:30', '12');  // "9:30 PM"
formatTime('9:41', '24');   // "09:41"
```

### 📅 可编辑日期选择器

#### 交互特性

- **悬停触发**: 鼠标悬停在 Date 按钮上自动显示
- **点击外部关闭**: 点击选择器外部区域自动关闭
- **保持打开**: 选择日期后保持打开状态，方便连续调整
- **实时验证**: 输入时实时验证日期时间有效性

#### 可编辑字段

```typescript
// 可编辑的日期时间状态
const [editableDateTime, setEditableDateTime] = useState({
  year: '2024',
  month: '01',
  day: '25',
  hours: '14',
  minutes: '30'
});
```

#### 时间生成

```typescript
// 生成 ISO 格式的日期时间字符串
const dateTimeString = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}:00`;

// 示例输出: "2024-01-25T14:30:00"
```

## 技术实现

### 状态管理

```typescript
// 时间状态管理
const [deviceTime, setDeviceTime] = useState('9:41');
const [timeFormat, setTimeFormat] = useState<TimeFormat>('12');
const [timeError, setTimeError] = useState('');
const [isTimeValid, setIsTimeValid] = useState(true);
```

### 输入处理

```typescript
// 格式化时间输入处理
const handleFormattedTimeChange = useCallback((value: string) => {
  // 格式化输入
  const formattedValue = formatTimeInput(value, timeFormat);
  
  // 验证边界值
  if (formattedValue.includes(':')) {
    const [hours, minutes] = formattedValue.split(':');
    if (!isValidTimeInput(hours, minutes || '', timeFormat)) {
      return; // 无效输入，不更新状态
    }
  }
  
  setDeviceTime(formattedValue);
  
  // 验证完整时间格式
  const validation = validateTimeFormat(formattedValue, timeFormat);
  setIsTimeValid(validation.isValid);
  setTimeError(validation.error);
}, [timeFormat]);
```

### 格式切换

```typescript
// 时间格式切换处理
const handleTimeFormatChange = useCallback((newFormat: TimeFormat) => {
  setTimeFormat(newFormat);
  
  // 重新验证当前时间
  if (deviceTime.trim()) {
    const validation = validateTimeFormat(deviceTime, newFormat);
    setIsTimeValid(validation.isValid);
    setTimeError(validation.error);
  }
}, [deviceTime]);
```

## 使用场景

### 设备时间设置

```typescript
// 在主界面中使用
<input
  type="text"
  value={deviceTime}
  onChange={(e) => handleFormattedTimeChange(e.target.value)}
  placeholder={timeFormat === '12' ? 'H:MM' : 'HH:MM'}
  maxLength={timeFormat === '12' ? 4 : 5}
/>
```

### 消息时间戳

```typescript
// 在 MessageInput 组件中使用
<Calendar
  onDateSelect={handleDateSelect}
  onClose={() => setShowDatePicker(false)}
  disabled={disabled}
/>
```

### 状态栏时间显示

```typescript
// 在 PhonePreview 组件中使用
<span className="status-bar-time">
  {formatDisplayTime(deviceTime, timeFormat)}
</span>
```

## 最佳实践

### 1. 输入验证

- 始终使用 `isValidTimeInput` 进行边界值验证
- 使用 `validateTimeFormat` 进行完整格式验证
- 提供清晰的错误提示信息

### 2. 格式转换

- 使用 `formatDisplayTime` 用于状态栏时间显示
- 使用 `formatTime` 用于需要 AM/PM 的场景
- 保持格式一致性

### 3. 用户体验

- 提供实时输入掩码
- 显示格式提示信息
- 支持格式快速切换

### 4. 性能优化

- 使用 `useCallback` 优化事件处理函数
- 避免不必要的状态更新
- 缓存验证结果

## 错误处理

### 常见错误类型

```typescript
// 时间格式错误
"Please enter a valid 12-hour time format (e.g. 9:41 or 12:30)"
"Please enter a valid 24-hour time format (e.g. 09:41 or 21:30)"

// 空时间错误
"Please enter a time"

// 边界值错误
// 通过 isValidTimeInput 预防，不会产生错误信息
```

### 错误恢复

```typescript
// 重置时间到默认值
const resetTime = useCallback(() => {
  setDeviceTime('9:41');
  setTimeFormat('12');
  setTimeError('');
  setIsTimeValid(true);
}, []);
```

## 扩展性

### 添加新的时间格式

```typescript
// 扩展 TimeFormat 类型
type TimeFormat = '12' | '24' | 'custom';

// 添加新的验证规则
const validateCustomFormat = (time: string): boolean => {
  // 自定义验证逻辑
};
```

### 国际化支持

```typescript
// 添加多语言错误信息
const getErrorMessage = (errorType: string, locale: string): string => {
  // 返回本地化的错误信息
};
```

## 总结

高级时间管理系统为假短信生成器提供了完整的时间处理能力，包括：

- ✅ 智能输入掩码和实时验证
- ✅ 12/24小时制格式切换
- ✅ 可编辑的日期选择器
- ✅ 边界值验证和错误处理
- ✅ 高性能的状态管理
- ✅ 优秀的用户体验

该系统确保了时间输入的准确性、一致性和用户友好性，为项目的整体质量提供了重要保障。
