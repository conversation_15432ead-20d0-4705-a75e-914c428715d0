/**
 * Notification System - Unified Export
 * 
 * This module provides a complete notification system that replaces native browser dialogs
 * with custom Apple-style components. It supports both toast notifications and modal dialogs
 * with full accessibility features.
 */

// Core components
export { NotificationProvider, useNotificationContext } from './NotificationProvider';
export { default as BaseNotification } from './BaseNotification';
export { default as Modal } from './Modal';
export { default as NotificationRenderer } from './NotificationRenderer';

// Hooks
export { useNotification } from './useNotification';

// Types
export type {
  NotificationType,
  NotificationStatus,
  ModalType,
  ButtonConfig,
  BaseNotificationConfig,
  ToastConfig,
  ModalConfig,
  NotificationConfig,
  NotificationState,
  NotificationContextType,
  ConfirmOptions,
  AlertOptions,
  SimpleToastOptions,
  AnimationState,
  BaseNotificationProps,
  ModalProps,
  ToastProps,
  NotificationTheme,
} from './types';

// Constants
export {
  DEFAULT_TOAST_CONFIG,
  DEFAULT_MODAL_CONFIG,
  STATUS_COLOR_MAP,
  MODAL_TYPE_STATUS_MAP,
} from './types';

// Default export for convenience
export { NotificationProvider as default } from './NotificationProvider';
