import { TutorialStep } from '../types/message';

/**
 * 教程步骤配置数据
 * 定义完整的交互式教程流程
 */
export const tutorialSteps: TutorialStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to Fake Text Message Tutorial',
    description: 'This interactive tutorial will guide you through creating realistic iPhone text message conversations.',
    content: 'We\'ll learn all the features step by step, including setting up contacts, adding messages, adjusting device settings, and more. The entire process takes about 5-10 minutes.',
    position: 'center',
    action: 'wait',
    isSkippable: true,
    autoNext: false
  },
  {
    id: 'recipient-name',
    title: 'Set Recipient Name',
    description: 'First, let\'s set the recipient name that will appear in the conversation.',
    content: 'Enter the recipient\'s name in the "Recipient Name" input field. This name will appear at the top of the iPhone Messages interface.',
    targetSelector: '[data-tutorial="recipient-name"]',
    position: 'right',
    action: 'input',
    demoData: {
      recipientName: '<PERSON>'
    },
    isSkippable: false,
    autoNext: false
  },
  {
    id: 'recipient-avatar',
    title: 'Upload Recipient Avatar (Optional)',
    description: 'You can add a custom avatar image for the contact.',
    content: 'Click the avatar upload button to select an image file. Supports JPG, PNG formats, file size up to 5MB.',
    targetSelector: '[data-tutorial="avatar-upload"]',
    position: 'right',
    action: 'click',
    isSkippable: true,
    autoNext: false
  },
  {
    id: 'add-message',
    title: '添加第一条消息',
    description: '现在让我们添加第一条消息到对话中。',
    content: '在消息输入框中输入文本内容。您可以输入任何想要显示的文字。',
    targetSelector: '[data-tutorial="message-input"]',
    position: 'top',
    action: 'input',
    demoData: { 
      content: '你好！这是一条测试消息。',
      actor: 'recipient'
    },
    isSkippable: false,
    autoNext: false
  },
  {
    id: 'message-roles',
    title: '选择消息发送者角色',
    description: '使用角色选择器来决定消息的发送方。',
    content: '• 灰色圆点 = 接收的消息（显示在左侧）\n• 蓝色圆点 = 发送的消息（显示在右侧）\n• 绿色圆点 = 系统消息（如iMessage、短信等）',
    targetSelector: '[data-tutorial="role-selector"]',
    position: 'bottom',
    action: 'click',
    isSkippable: false,
    autoNext: false
  },
  {
    id: 'add-image',
    title: '添加图片消息（可选）',
    description: '您还可以在消息中添加图片。',
    content: '点击图片上传按钮选择要添加的图片。图片将显示在消息气泡中，就像真实的iPhone消息一样。',
    targetSelector: '[data-tutorial="image-upload"]',
    position: 'bottom',
    action: 'click',
    isSkippable: true,
    autoNext: false
  },
  {
    id: 'add-more-messages',
    title: '添加更多消息',
    description: '点击"添加消息"按钮创建对话。',
    content: '您可以添加多条消息来创建完整的对话。每条消息都可以设置不同的发送者角色。',
    targetSelector: '[data-tutorial="add-message-button"]',
    position: 'top',
    action: 'click',
    demoData: {
      content: '好的，收到了！',
      actor: 'sender'
    },
    isSkippable: true,
    autoNext: false
  },
  {
    id: 'time-settings',
    title: '调整显示时间',
    description: '自定义iPhone状态栏中显示的时间。',
    content: '点击时间输入框来修改显示时间。支持12小时制（如 9:41 AM）和24小时制（如 21:41）格式。',
    targetSelector: '[data-tutorial="time-input"]',
    position: 'left',
    action: 'click',
    demoData: { 
      time: '10:30',
      format: '12'
    },
    isSkippable: true,
    autoNext: false
  },
  {
    id: 'time-format',
    title: '切换时间格式',
    description: '选择12小时制或24小时制时间格式。',
    content: '12小时制会显示AM/PM，24小时制使用00:00-23:59格式。选择您偏好的格式。',
    targetSelector: '[data-tutorial="time-format"]',
    position: 'left',
    action: 'click',
    isSkippable: true,
    autoNext: false
  },
  {
    id: 'battery-settings',
    title: '设置电池电量',
    description: '调整iPhone状态栏中显示的电池电量。',
    content: '使用滑块来设置电池电量百分比（0-100%）。您还可以选择是否显示电量百分比数字。',
    targetSelector: '[data-tutorial="battery-slider"]',
    position: 'left',
    action: 'input',
    demoData: { 
      batteryPercentage: 85
    },
    isSkippable: true,
    autoNext: false
  },
  {
    id: 'theme-mode',
    title: '切换主题模式',
    description: '选择浅色或深色主题模式。',
    content: '浅色模式模拟白天的iPhone界面，深色模式模拟夜间模式。选择最适合您需求的主题。',
    targetSelector: '[data-tutorial="theme-toggle"]',
    position: 'left',
    action: 'click',
    isSkippable: true,
    autoNext: false
  },
  {
    id: 'preview-result',
    title: '实时预览效果',
    description: '右侧的iPhone预览会实时显示您的所有设置。',
    content: '所有的更改都会立即反映在预览中，确保您看到的就是最终效果。预览完全模拟真实的iPhone界面。',
    targetSelector: '[data-tutorial="phone-preview"]',
    position: 'left',
    action: 'demo',
    isSkippable: true,
    autoNext: false
  },
  {
    id: 'message-management',
    title: '管理消息',
    description: '您可以编辑、删除或重新排序消息。',
    content: '点击消息可以编辑内容，使用删除按钮移除消息，或拖拽来重新排序对话。',
    targetSelector: '[data-tutorial="message-list"]',
    position: 'top',
    action: 'demo',
    isSkippable: true,
    autoNext: false
  },
  {
    id: 'download-image',
    title: '下载高质量截图',
    description: '完成设置后，您可以下载高质量的截图。',
    content: '点击"下载图片"按钮生成并保存您的假短信对话截图。图片将以PNG格式保存，质量清晰。',
    targetSelector: '[data-tutorial="download-button"]',
    position: 'top',
    action: 'click',
    isSkippable: true,
    autoNext: false
  },
  {
    id: 'advanced-features',
    title: '探索高级功能',
    description: '了解更多高级功能和技巧。',
    content: '• 支持多种消息状态（已送达、已读等）\n• 可以设置自定义时间戳\n• 支持长按消息显示时间\n• 完全响应式设计，支持移动设备',
    position: 'center',
    action: 'wait',
    isSkippable: true,
    autoNext: false
  },
  {
    id: 'completion',
    title: '教程完成！🎉',
    description: '恭喜！您已经掌握了假短信生成器的所有主要功能。',
    content: '现在您可以创建各种逼真的iPhone短信对话截图了。如需重新学习任何功能，可以随时返回教程页面。感谢您的使用！',
    position: 'center',
    action: 'wait',
    isSkippable: false,
    autoNext: false
  }
];

/**
 * 教程配置选项
 */
export const tutorialConfig = {
  // 总步骤数
  totalSteps: tutorialSteps.length,
  
  // 默认设置
  defaultSettings: {
    autoAdvance: false,
    showProgress: true,
    allowSkip: true,
    keyboardNavigation: true
  },
  
  // 演示数据
  demoData: {
    recipientName: '张三',
    messages: [
      {
        id: 'demo-1',
        sender: 'recipient' as const,
        content: '你好！这是一条演示消息。',
        timestamp: '10:30',
        status: 'default' as const,
        method: 'data' as const
      },
      {
        id: 'demo-2', 
        sender: 'user' as const,
        content: '收到了，谢谢！',
        timestamp: '10:31',
        status: 'delivered' as const,
        method: 'data' as const
      }
    ],
    deviceTime: '10:30',
    timeFormat: '12' as const,
    batteryPercentage: 85,
    mode: 'light' as const
  },
  
  // 键盘快捷键
  keyboardShortcuts: {
    next: ['ArrowRight', 'Enter', 'Space'],
    previous: ['ArrowLeft'],
    skip: ['s', 'S'],
    close: ['Escape', 'q', 'Q']
  }
};

/**
 * 获取指定步骤的数据
 */
export function getTutorialStep(stepId: string): TutorialStep | undefined {
  return tutorialSteps.find(step => step.id === stepId);
}

/**
 * 获取步骤索引
 */
export function getStepIndex(stepId: string): number {
  return tutorialSteps.findIndex(step => step.id === stepId);
}

/**
 * 检查是否为最后一步
 */
export function isLastStep(stepIndex: number): boolean {
  return stepIndex === tutorialSteps.length - 1;
}

/**
 * 检查是否为第一步
 */
export function isFirstStep(stepIndex: number): boolean {
  return stepIndex === 0;
}

/**
 * 获取下一步骤
 */
export function getNextStep(currentIndex: number): TutorialStep | null {
  if (currentIndex < tutorialSteps.length - 1) {
    return tutorialSteps[currentIndex + 1];
  }
  return null;
}

/**
 * 获取上一步骤
 */
export function getPreviousStep(currentIndex: number): TutorialStep | null {
  if (currentIndex > 0) {
    return tutorialSteps[currentIndex - 1];
  }
  return null;
}
