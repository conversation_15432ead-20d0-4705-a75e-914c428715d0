/**
 * 统一的消息类型定义
 * 用于整个应用中的消息数据结构
 */

export interface Message {
  id: string;
  sender: 'user' | 'recipient' | 'system';
  content: string;
  timestamp?: string;
  status?: 'default' | 'delivered' | 'failed' | 'read';
  imageUrl?: string;
  method?: 'data' | 'image' | 'unknown';
}

export interface DraftMessage {
  id: string;
  actor: 'sender' | 'recipient' | 'system';
  content: string;
  imageFile?: File;
  imageUrl?: string;
  timestamp?: string;
  status?: 'default' | 'delivered' | 'failed' | 'read';
  method: 'data' | 'image' | 'unknown';
}

export type TimeFormat = '12' | '24';
export type ThemeMode = 'light' | 'dark';
export type MessageSender = 'user' | 'recipient' | 'system';

export interface PhonePreviewProps {
  recipientName: string;
  recipientAvatar: string | null;
  messages: Message[];
  deviceTime: string;
  timeFormat: TimeFormat;
  mode: ThemeMode;
  batteryPercentage?: number; // 电池电量百分比 (0-100)
}

export interface MessageBubbleProps {
  message: Message;
  recipientName: string;
  recipientAvatar: string | null;
  mode: ThemeMode;
  timeFormat: TimeFormat;
  showTime?: boolean;
  isFirstMessage?: boolean; // 是否是第一个消息
  shouldShowTimestamp?: boolean; // 是否应该显示时间戳
}

// 角色配置类型
export interface RoleConfig {
  color: string;
  label: string;
  mappedSender: MessageSender;
}

export const ROLE_CONFIG: Record<string, RoleConfig> = {
  recipient: {
    color: 'rgb(209, 209, 211)', // Gray
    label: 'Recipient',
    mappedSender: 'recipient' as const
  },
  sender: {
    color: 'rgb(0, 122, 255)', // Blue
    label: 'User/Sender',
    mappedSender: 'user' as const
  },
  system: {
    color: 'rgb(52, 199, 89)', // Green
    label: 'System/Third Party',
    mappedSender: 'system' as const
  }
} as const;

// 教程相关类型定义
export type TutorialPosition = 'top' | 'bottom' | 'left' | 'right' | 'center';

export interface TutorialStep {
  id: string;                    // 步骤唯一标识符
  title: string;                 // 步骤标题
  description: string;           // 步骤描述
  content?: string;              // 详细内容（可选）
  targetSelector?: string;       // 目标元素选择器（用于高亮）
  position: TutorialPosition;    // 提示框位置
  action?: 'click' | 'input' | 'wait' | 'demo'; // 期望的用户操作
  demoData?: any;               // 演示数据（可选）
  isSkippable?: boolean;        // 是否可跳过
  autoNext?: boolean;           // 是否自动进入下一步
  delay?: number;               // 自动进入下一步的延迟时间（毫秒）
}

export interface TutorialConfig {
  id: string;                   // 教程唯一标识符
  title: string;                // 教程标题
  description: string;          // 教程描述
  steps: TutorialStep[];        // 教程步骤列表
  totalSteps: number;           // 总步骤数
  currentStep: number;          // 当前步骤索引
  isCompleted: boolean;         // 是否已完成
  canRestart: boolean;          // 是否可重新开始
}

export interface TutorialState {
  isActive: boolean;            // 教程是否激活
  currentStepIndex: number;     // 当前步骤索引
  completedSteps: string[];     // 已完成的步骤ID列表
  skippedSteps: string[];       // 已跳过的步骤ID列表
  startTime?: Date;             // 教程开始时间
  endTime?: Date;               // 教程结束时间
}
