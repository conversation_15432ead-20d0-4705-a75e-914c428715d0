# 贡献指南

感谢您对假短信生成器项目的关注！我们欢迎所有形式的贡献，包括但不限于代码、文档、设计、测试和反馈。

## 🤝 如何贡献

### 报告问题
如果您发现了 Bug 或有功能建议：
1. 查看 [现有 Issues](https://github.com/yourusername/fake-text-message-generator/issues) 避免重复
2. 使用合适的 Issue 模板创建新问题
3. 提供详细的描述和复现步骤

### 提交代码
1. Fork 项目到您的 GitHub 账户
2. 创建功能分支：`git checkout -b feature/amazing-feature`
3. 提交您的更改：`git commit -m 'Add amazing feature'`
4. 推送到分支：`git push origin feature/amazing-feature`
5. 创建 Pull Request

## 📋 开发规范

### 代码风格
- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint 和 Prettier 配置
- 组件使用函数式组件和 Hooks
- 保持代码简洁和可读性

### 命名规范
- **组件**: PascalCase (如 `MessageBubble`)
- **文件**: kebab-case (如 `message-bubble.tsx`)
- **变量/函数**: camelCase (如 `handleTimeChange`)
- **常量**: UPPER_SNAKE_CASE (如 `ROLE_CONFIG`)

### 提交信息
使用语义化提交信息：
```
feat: 添加新功能
fix: 修复问题
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 构建过程或辅助工具的变动
```

## 🧪 测试要求

### 单元测试
- 为新功能编写单元测试
- 确保测试覆盖率不低于 80%
- 使用 Jest 和 React Testing Library

### 集成测试
- 测试组件间的交互
- 验证用户操作流程
- 确保功能正常工作

## 📚 文档要求

### 代码文档
- 为公共 API 添加 JSDoc 注释
- 复杂逻辑需要内联注释
- 更新相关的 TypeScript 类型定义

### 用户文档
- 新功能需要更新使用文档
- 提供完整的代码示例
- 保持文档与代码同步

## 🎨 设计规范

### UI/UX 原则
- 遵循 Apple 设计语言
- 保持界面简洁和一致性
- 优先考虑用户体验
- 支持无障碍访问

### 响应式设计
- 移动端优先设计
- 适配不同屏幕尺寸
- 保证在各设备上的可用性

## 🔧 开发环境

### 环境要求
- Node.js 18+
- npm/yarn/pnpm
- Git

### 本地开发
```bash
# 克隆项目
git clone https://github.com/yourusername/fake-text-message-generator.git
cd fake-text-message-generator

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 运行测试
npm run test

# 代码检查
npm run lint
```

## 📝 Pull Request 指南

### PR 要求
- 清晰的标题和描述
- 关联相关的 Issue
- 包含必要的测试
- 通过所有 CI 检查
- 更新相关文档

### 审查流程
1. 自动化测试检查
2. 代码质量审查
3. 功能测试验证
4. 文档完整性检查
5. 合并到主分支

## 🏷️ 发布流程

### 版本管理
- 遵循语义化版本控制 (SemVer)
- 主要版本：破坏性变更
- 次要版本：新功能添加
- 补丁版本：问题修复

### 发布步骤
1. 更新版本号
2. 生成变更日志
3. 创建 Git 标签
4. 发布到 npm (如适用)
5. 部署到生产环境

## 🎯 贡献领域

### 代码贡献
- 新功能开发
- Bug 修复
- 性能优化
- 代码重构

### 非代码贡献
- 文档改进
- 设计优化
- 测试用例
- 问题反馈

### 特别需要的贡献
- 🌍 国际化支持
- 📱 更多设备适配
- ♿ 无障碍功能
- 🎨 UI/UX 改进
- 📊 性能监控
- 🧪 自动化测试

## 📞 联系方式

### 获取帮助
- [GitHub Issues](https://github.com/yourusername/fake-text-message-generator/issues) - 问题报告
- [GitHub Discussions](https://github.com/yourusername/fake-text-message-generator/discussions) - 技术讨论
- [项目主页](https://faketextmessage.xyz) - 在线演示

### 社区交流
- 参与 GitHub Discussions
- 关注项目更新
- 分享使用经验

## 🙏 致谢

感谢所有为项目做出贡献的开发者、设计师和用户！您的每一份贡献都让项目变得更好。

### 贡献者名单
- 查看 [Contributors](https://github.com/yourusername/fake-text-message-generator/graphs/contributors)
- 感谢每一位贡献者的付出

## 📄 许可证

通过贡献代码，您同意您的贡献将在 [MIT License](../../LICENSE) 下授权。

---

<div align="center">

**🚀 让我们一起打造更好的假短信生成器！**

[开始贡献](https://github.com/yourusername/fake-text-message-generator/fork) • [查看 Issues](https://github.com/yourusername/fake-text-message-generator/issues) • [参与讨论](https://github.com/yourusername/fake-text-message-generator/discussions)

</div>
