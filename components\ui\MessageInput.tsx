'use client';

import { useState, useRef, useEffect, useCallback, useMemo, forwardRef, useImperativeHandle } from 'react';
import { useNotification } from './notification';
import { Message, DraftMessage, ROLE_CONFIG } from '../../types/message';
import { fileValidator, createUserFriendlyError } from '../../utils/errorHandling';
import { accessibilityManager } from '../../utils/accessibility';

// Updated props interface to maintain compatibility
interface MessageInputProps {
  value?: string; // Made optional for backward compatibility
  onChange?: (value: string) => void; // Made optional for backward compatibility
  onAddUser: () => void;
  onAddRecipient: () => void;
  placeholder?: string;
  maxLength?: number;
  disabled?: boolean;
  // New props for enhanced functionality
  messages?: Message[];
  onMessagesChange?: (messages: Message[]) => void;
}

// Ref interface for exposing methods to parent
interface MessageInputRef {
  resetMessages: () => void;
}

// Role Indicator Component
const RoleIndicator = ({
  actor,
  onActorChange,
  disabled = false
}: {
  actor: 'sender' | 'recipient' | 'system';
  onActorChange: (actor: 'sender' | 'recipient' | 'system') => void;
  disabled?: boolean;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const config = ROLE_CONFIG[actor];

  return (
    <div className="relative">
      <button
        className="focus-visible:ring-accent-600 flex h-7 w-7 shrink-0 cursor-pointer items-center justify-center focus-visible:rounded-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-inset disabled:opacity-50 disabled:cursor-not-allowed"
        type="button"
        role="combobox"
        tabIndex={0}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        aria-autocomplete="none"
        disabled={disabled}
        onClick={() => setIsOpen(!isOpen)}
        onBlur={() => setTimeout(() => setIsOpen(false), 150)}
      >
        <div
          className="focus-visible:ring-accent-600 size-[12px] rounded-full focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2"
          style={{ backgroundColor: config.color }}
        />
      </button>

      {/* Hidden form input for data storage */}
      <input
        tabIndex={-1}
        aria-hidden="true"
        value={JSON.stringify({ actor, method: "data" })}
        name="messages"
        style={{
          clip: 'rect(0px, 0px, 0px, 0px)',
          overflow: 'hidden',
          whiteSpace: 'nowrap',
          position: 'fixed',
          top: 0,
          left: 0,
          border: 0,
          padding: 0,
          width: '1px',
          height: '1px',
          margin: '-1px'
        }}
        readOnly
      />

      {/* Dropdown menu */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 min-w-[150px]">
          {Object.entries(ROLE_CONFIG).map(([key, roleConfig]) => (
            <button
              key={key}
              className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center gap-2 text-sm text-black"
              onClick={() => {
                onActorChange(key as 'sender' | 'recipient' | 'system');
                setIsOpen(false);
              }}
            >
              <div
                className="size-[12px] rounded-full"
                style={{ backgroundColor: roleConfig.color }}
              />
              {roleConfig.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

// Custom Calendar Component
const Calendar = ({
  onDateSelect,
  onClose,
  disabled = false
}: {
  onDateSelect: (date: string) => void;
  onClose: () => void;
  disabled?: boolean;
}) => {
  const today = new Date();
  const [currentDate, setCurrentDate] = useState(new Date());

  // 可编辑的日期和时间状态
  const [editableDateTime, setEditableDateTime] = useState({
    year: today.getFullYear().toString(),
    month: (today.getMonth() + 1).toString().padStart(2, '0'),
    day: today.getDate().toString().padStart(2, '0'),
    hours: today.getHours().toString().padStart(2, '0'),
    minutes: today.getMinutes().toString().padStart(2, '0')
  });

  // 移除自动同步逻辑，避免状态冲突
  // 让用户明确地选择日期，而不是自动更新

  // Get current month and year
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();

  // Month names
  const monthNames = [
    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
  ];

  // Day names
  const dayNames = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];

  // Get first day of month and number of days
  const firstDayOfMonth = new Date(currentYear, currentMonth, 1);
  const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0);
  const firstDayWeekday = firstDayOfMonth.getDay();
  const daysInMonth = lastDayOfMonth.getDate();

  // Get previous month's last days
  // 修复：获取当前月的第0天，这样会得到前一个月的最后一天
  const prevMonth = new Date(currentYear, currentMonth, 0);
  const daysInPrevMonth = prevMonth.getDate();



  // Generate calendar days
  const calendarDays = [];

  // Previous month's trailing days
  // 如果8月1日是星期五(5)，那么需要显示前5天：周日到周四
  // 即7月的27, 28, 29, 30, 31号
  for (let i = firstDayWeekday - 1; i >= 0; i--) {
    const prevMonthDay = daysInPrevMonth - i;


    // 验证前一个月的日期是否有效
    if (prevMonthDay < 1 || prevMonthDay > daysInPrevMonth) {
      console.warn('跳过超出范围的前月日期:', {
        计算出的日期: prevMonthDay,
        前月最大天数: daysInPrevMonth,
        说明: `前月日期${prevMonthDay}超出有效范围(1-${daysInPrevMonth})`
      });
      continue; // 跳过无效日期
    }

    // 创建前一个月的日期对象
    // 修复：正确计算前一个月的年份和月份
    const prevMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;
    const prevMonthIndex = currentMonth === 0 ? 11 : currentMonth - 1;
    const prevMonthDate = new Date(prevMonthYear, prevMonthIndex, prevMonthDay);

    // 验证创建的日期是否正确（防止JavaScript自动调整）
    if (prevMonthDate.getMonth() !== prevMonthIndex ||
        prevMonthDate.getDate() !== prevMonthDay ||
        prevMonthDate.getFullYear() !== prevMonthYear) {
      console.warn('跳过无效的前月日期:', {
        预期: { 年: prevMonthYear, 月: prevMonthIndex, 日: prevMonthDay },
        实际: { 年: prevMonthDate.getFullYear(), 月: prevMonthDate.getMonth(), 日: prevMonthDate.getDate() },
        说明: `前月日期${prevMonthDay}不存在或被JavaScript自动调整`
      });
      continue; // 跳过被自动调整的日期
    }

    calendarDays.push({
      day: prevMonthDay,
      isCurrentMonth: false,
      isPrevMonth: true,
      date: prevMonthDate
    });
  }

  // Current month days
  for (let day = 1; day <= daysInMonth; day++) {
    calendarDays.push({
      day,
      isCurrentMonth: true,
      isPrevMonth: false,
      date: new Date(currentYear, currentMonth, day)
    });
  }

  // Next month's leading days
  const remainingCells = 42 - calendarDays.length; // 6 rows × 7 days
  for (let day = 1; day <= remainingCells; day++) {
    calendarDays.push({
      day,
      isCurrentMonth: false,
      isPrevMonth: false,
      date: new Date(currentYear, currentMonth + 1, day)
    });
  }

  // Navigation functions
  const goToPrevMonth = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentDate(new Date(currentYear, currentMonth - 1, 1));
  };

  const goToNextMonth = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentDate(new Date(currentYear, currentMonth + 1, 1));
  };

  // 获取指定年月的最大天数
  // 注意：month 参数应该是1-12格式（不是0-11）
  const getMaxDaysInMonth = (year: number, month: number) => {
    // 将1-12格式的月份转换为0-11格式，然后获取下个月的第0天（即当月最后一天）
    return new Date(year, month, 0).getDate();
  };

  // 日期时间输入处理函数
  const handleDateTimeChange = (field: 'year' | 'month' | 'day' | 'hours' | 'minutes', value: string) => {
    // 只允许数字输入
    if (!/^\d*$/.test(value)) return;

    // 限制输入长度
    if ((field === 'year' && value.length > 4) ||
        (field !== 'year' && value.length > 2)) return;

    const numValue = parseInt(value) || 0;

    // 基本范围验证 - 只在输入完整时验证，避免阻止输入过程
    if (field === 'year' && value.length === 4 && (numValue < 1900 || numValue > 2100)) return;
    if (field === 'month' && value.length > 0 && (numValue < 1 || numValue > 12)) return;
    if (field === 'hours' && value.length > 0 && numValue > 23) return;
    if (field === 'minutes' && value.length > 0 && numValue > 59) return;

    // 对于日期字段，需要根据当前年月验证最大天数
    if (field === 'day') {
      const currentYear = parseInt(editableDateTime.year) || new Date().getFullYear();
      const currentMonth = parseInt(editableDateTime.month) || new Date().getMonth() + 1;
      const maxDays = getMaxDaysInMonth(currentYear, currentMonth);

      if (numValue < 1 || numValue > maxDays) return;
    }

    setEditableDateTime(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 输入失去焦点时格式化
  const handleDateTimeBlur = (field: 'year' | 'month' | 'day' | 'hours' | 'minutes') => {
    setEditableDateTime(prev => {
      let formattedValue = prev[field];

      if (field === 'year') {
        formattedValue = prev[field].padStart(4, '0');
      } else {
        formattedValue = prev[field].padStart(2, '0');
      }

      let newDateTime = {
        ...prev,
        [field]: formattedValue
      };

      // 如果修改了年份或月份，检查并调整日期
      if (field === 'year' || field === 'month') {
        const year = parseInt(field === 'year' ? formattedValue : prev.year);
        const month = parseInt(field === 'month' ? formattedValue : prev.month);
        const day = parseInt(prev.day);

        const maxDays = getMaxDaysInMonth(year, month);
        if (day > maxDays) {
          newDateTime.day = maxDays.toString().padStart(2, '0');
        }
      }

      return newDateTime;
    });
  };

  // Date selection
  const handleDateClick = (date: Date, e: React.MouseEvent) => {
    e.stopPropagation();

    // 更新编辑的日期时间状态
    const newDateTime = {
      year: date.getFullYear().toString(),
      month: (date.getMonth() + 1).toString().padStart(2, '0'),
      day: date.getDate().toString().padStart(2, '0'),
      hours: editableDateTime.hours,
      minutes: editableDateTime.minutes
    };

    setEditableDateTime(newDateTime);

    // 创建包含自定义日期时间的字符串
    const dateTimeString = `${newDateTime.year}-${newDateTime.month}-${newDateTime.day}T${newDateTime.hours}:${newDateTime.minutes}:00`;

    onDateSelect(dateTimeString);
    // 移除自动关闭逻辑，保持日期选择器打开状态
  };

  // 手动输入日期时间后生成完整字符串并更新日历显示
  const generateDateTimeString = () => {
    // 验证输入的数值
    const year = parseInt(editableDateTime.year);
    const month = parseInt(editableDateTime.month);
    const day = parseInt(editableDateTime.day);
    const hours = parseInt(editableDateTime.hours);
    const minutes = parseInt(editableDateTime.minutes);

    // 检查基本范围
    if (isNaN(year) || isNaN(month) || isNaN(day) || isNaN(hours) || isNaN(minutes)) {
      console.warn('Invalid date/time values:', editableDateTime);
      return;
    }

    if (year < 1900 || year > 2100 ||
        month < 1 || month > 12 ||
        day < 1 || day > 31 ||
        hours < 0 || hours > 23 ||
        minutes < 0 || minutes > 59) {
      console.warn('Date/time values out of range:', editableDateTime);
      return;
    }

    // 创建并验证日期对象
    const newDate = new Date(year, month - 1, day, hours, minutes, 0);

    // 验证日期是否有效（检查是否被JavaScript自动调整）
    if (newDate.getFullYear() !== year ||
        newDate.getMonth() !== (month - 1) ||
        newDate.getDate() !== day ||
        newDate.getHours() !== hours ||
        newDate.getMinutes() !== minutes) {
      console.warn('Invalid date created:', { input: editableDateTime, result: newDate });
      return;
    }

    // 生成日期时间字符串
    const dateTimeString = `${editableDateTime.year}-${editableDateTime.month.padStart(2, '0')}-${editableDateTime.day.padStart(2, '0')}T${editableDateTime.hours.padStart(2, '0')}:${editableDateTime.minutes.padStart(2, '0')}:00`;

    // 更新日历显示
    setCurrentDate(newDate);

    onDateSelect(dateTimeString);
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDateSelect('');
    // 重置为当前日期时间
    const now = new Date();
    setEditableDateTime({
      year: now.getFullYear().toString(),
      month: (now.getMonth() + 1).toString().padStart(2, '0'),
      day: now.getDate().toString().padStart(2, '0'),
      hours: now.getHours().toString().padStart(2, '0'),
      minutes: now.getMinutes().toString().padStart(2, '0')
    });
    onClose();
  };

  // Prevent calendar from closing when clicking inside
  const handleCalendarClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  // Check if date is today
  const isToday = (date: Date) => {
    return date.toDateString() === today.toDateString();
  };

  // Check if date is selected - 使用最严格的匹配逻辑
  const isSelected = (date: Date) => {
    // 解析输入的日期时间
    const inputYear = parseInt(editableDateTime.year);
    const inputMonth = parseInt(editableDateTime.month);
    const inputDay = parseInt(editableDateTime.day);

    // 验证输入值是否有效
    if (isNaN(inputYear) || isNaN(inputMonth) || isNaN(inputDay)) {
      return false;
    }

    // 验证日期范围
    if (inputMonth < 1 || inputMonth > 12 || inputDay < 1 || inputDay > 31) {
      return false;
    }

    // 获取传入日期的年月日
    const dateYear = date.getFullYear();
    const dateMonth = date.getMonth() + 1; // 转换为1-12格式
    const dateDay = date.getDate();

    // 精确匹配，确保年月日完全相同
    const isMatch = dateYear === inputYear &&
                   dateMonth === inputMonth &&
                   dateDay === inputDay;

    // 额外验证：确保输入的日期在该月份中确实存在
    if (isMatch) {
      const maxDaysInInputMonth = getMaxDaysInMonth(inputYear, inputMonth);
      if (inputDay > maxDaysInInputMonth) {
        return false; // 输入的日期无效（如6月31日）
      }
    }

    return isMatch;
  };

  return (
    <div
      className="absolute top-0 left-full ml-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[280px] p-4"
      onClick={handleCalendarClick}
    >
      {/* Header with editable date and time - iPhone style */}
      <div className="bg-gray-50 -m-4 mb-4 p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center text-sm font-medium text-black">
            <input
              type="text"
              placeholder="YYYY"
              value={editableDateTime.year}
              onChange={(e) => handleDateTimeChange('year', e.target.value)}
              onBlur={() => {
                handleDateTimeBlur('year');
                generateDateTimeString();
              }}
              className="w-10 text-center bg-transparent border-none outline-none text-black font-medium text-sm"
              disabled={disabled}
              maxLength={4}
            />
            <span className="text-black">/</span>
            <input
              type="text"
              placeholder="MM"
              value={editableDateTime.month}
              onChange={(e) => handleDateTimeChange('month', e.target.value)}
              onBlur={() => {
                handleDateTimeBlur('month');
                generateDateTimeString();
              }}
              className="w-6 text-center bg-transparent border-none outline-none text-black font-medium text-sm"
              disabled={disabled}
              maxLength={2}
            />
            <span className="text-black">/</span>
            <input
              type="text"
              placeholder="DD"
              value={editableDateTime.day}
              onChange={(e) => handleDateTimeChange('day', e.target.value)}
              onBlur={() => {
                handleDateTimeBlur('day');
                generateDateTimeString();
              }}
              className="w-6 text-center bg-transparent border-none outline-none text-black font-medium text-sm"
              disabled={disabled}
              maxLength={2}
            />
          </div>
          <div className="flex items-center text-sm font-medium text-black">
            <input
              type="text"
              placeholder="HH"
              value={editableDateTime.hours}
              onChange={(e) => handleDateTimeChange('hours', e.target.value)}
              onBlur={() => {
                handleDateTimeBlur('hours');
                generateDateTimeString();
              }}
              className="w-6 text-center bg-transparent border-none outline-none text-black font-medium text-sm"
              disabled={disabled}
              maxLength={2}
            />
            <span className="text-black">:</span>
            <input
              type="text"
              placeholder="MM"
              value={editableDateTime.minutes}
              onChange={(e) => handleDateTimeChange('minutes', e.target.value)}
              onBlur={() => {
                handleDateTimeBlur('minutes');
                generateDateTimeString();
              }}
              className="w-6 text-center bg-transparent border-none outline-none text-black font-medium text-sm"
              disabled={disabled}
              maxLength={2}
            />
          </div>
        </div>
      </div>

      {/* Month navigation */}
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={goToPrevMonth}
          disabled={disabled}
          className="p-1 hover:bg-gray-100 rounded disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        <div className="text-sm font-medium text-gray-900">
          {monthNames[currentMonth]} {currentYear}
        </div>

        <button
          onClick={goToNextMonth}
          disabled={disabled}
          className="p-1 hover:bg-gray-100 rounded disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>

      {/* Day headers */}
      <div className="grid grid-cols-7 gap-1 mb-2">
        {dayNames.map((day) => (
          <div key={day} className="text-xs text-gray-500 text-center py-1 font-medium">
            {day}
          </div>
        ))}
      </div>

      {/* Calendar grid */}
      <div className="grid grid-cols-7 gap-1 mb-4">
        {calendarDays.slice(0, 42).map((calendarDay, index) => {
          const isCurrentMonthDay = calendarDay.isCurrentMonth;
          const isTodayDate = isToday(calendarDay.date);
          const isSelectedDate = isSelected(calendarDay.date);

          return (
            <button
              key={index}
              onClick={(e) => handleDateClick(calendarDay.date, e)}
              disabled={disabled}
              className={`
                w-8 h-8 text-sm rounded-md transition-colors duration-200 disabled:cursor-not-allowed
                ${isCurrentMonthDay
                  ? 'text-gray-900 hover:bg-gray-100'
                  : 'text-gray-300 hover:bg-gray-50'
                }
                ${isTodayDate
                  ? 'bg-blue-500 text-white hover:bg-blue-600'
                  : ''
                }
                ${isSelectedDate && !isTodayDate
                  ? 'bg-blue-100 text-blue-600'
                  : ''
                }
              `}
            >
              {calendarDay.day}
            </button>
          );
        })}
      </div>

      {/* Action buttons */}
      <div className="flex gap-2 pt-3 border-t border-gray-100">
        <button
          onClick={handleClear}
          disabled={disabled}
          className="flex-1 px-3 py-2 text-sm text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Clear
        </button>
      </div>
    </div>
  );
};

// Menu Item Interface
interface MenuItem {
  label: string;
  action: () => void;
  isDate?: boolean;
  isStatus?: boolean;
  danger?: boolean;
}

// Message Menu Component
const MessageMenu = ({
  messageId,
  messageActor,
  messageStatus = 'default',
  onDelete,
  onSetTimestamp,
  onSetStatus,
  onUpdate,
  disabled = false
}: {
  messageId: string;
  messageActor: 'sender' | 'recipient' | 'system';
  messageStatus?: 'default' | 'delivered' | 'failed' | 'read';
  onDelete: (id: string) => void;
  onSetTimestamp: (id: string, date?: string) => void;
  onSetStatus: (id: string) => void;
  onUpdate: (id: string, updates: Partial<DraftMessage>) => void;
  disabled?: boolean;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showStatusPicker, setShowStatusPicker] = useState(false);
  const datePickerRef = useRef<HTMLDivElement>(null); // 日期选择器容器引用
  const statusPickerRef = useRef<HTMLDivElement>(null); // 状态选择器容器引用
  const menuContainerRef = useRef<HTMLDivElement>(null); // 整个菜单容器引用
  const notification = useNotification();

  // 统一处理点击外部区域关闭选择器的逻辑
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;

      // 如果点击在整个菜单容器内，不关闭任何选择器
      if (menuContainerRef.current && menuContainerRef.current.contains(target)) {
        return;
      }

      // 检查是否点击在日期选择器外部
      if (showDatePicker && datePickerRef.current && !datePickerRef.current.contains(target)) {
        setShowDatePicker(false);
      }

      // 检查是否点击在状态选择器外部
      if (showStatusPicker && statusPickerRef.current && !statusPickerRef.current.contains(target)) {
        setShowStatusPicker(false);
      }
    };

    // 只有当至少有一个选择器打开时才添加事件监听器
    if (showDatePicker || showStatusPicker) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showDatePicker, showStatusPicker]);



  const handleDelete = async () => {
    const confirmed = await notification.modal.confirmDanger(
      'Are you sure you want to delete this message?',
      {
        title: 'Delete Message',
        confirmText: 'Delete',
        cancelText: 'Cancel'
      }
    );
    if (confirmed) {
      onDelete(messageId);
    }
    setIsOpen(false);
  };

  const handleDateHover = () => {
    // 实现互斥显示：显示日期选择器前先关闭状态选择器
    if (showStatusPicker) {
      setShowStatusPicker(false);
    }
    setShowDatePicker(true);
  };

  const handleDateSelect = (date: string) => {
    onSetTimestamp(messageId, date);
    // 移除自动关闭逻辑，保持日期选择器打开状态
  };

  const handleStatusHover = () => {
    // 实现互斥显示：显示状态选择器前先关闭日期选择器
    if (showDatePicker) {
      setShowDatePicker(false);
    }
    setShowStatusPicker(true);
  };

  const handleStatusSelect = (status: 'default' | 'delivered' | 'failed' | 'read') => {
    // 使用onUpdate函数更新消息状态
    onUpdate(messageId, { status });
    // 显示成功通知
    const statusLabels = {
      'default': 'Default',
      'delivered': 'Delivered',
      'failed': 'Not Delivered',
      'read': 'Read'
    };
    notification.toast.success(`Status set to: ${statusLabels[status]}`);
    // 保持状态选择器打开状态，与日期选择器行为一致
  };

  const menuItems: MenuItem[] = [
    {
      label: 'Date',
      action: () => {}, // No action on click, handled by hover
      isDate: true
    },
    ...(messageActor !== 'recipient' ? [{
      label: 'Status',
      action: () => {}, // No action on click, handled by hover
      isStatus: true
    }] : []),
    { label: 'Delete', action: handleDelete, danger: true }
  ];

  return (
    <div className="relative" ref={menuContainerRef}>
      <button
        className="focus-visible:ring-accent-600 group flex h-7 w-7 shrink-0 cursor-pointer items-center justify-center focus-visible:rounded-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-inset disabled:opacity-50 disabled:cursor-not-allowed"
        type="button"
        tabIndex={0}
        aria-haspopup="menu"
        aria-expanded={isOpen}
        disabled={disabled}
        onClick={() => setIsOpen(!isOpen)}
        onBlur={() => {
          // 如果日期选择器或状态选择器打开，不关闭菜单
          if (!showDatePicker && !showStatusPicker) {
            setTimeout(() => setIsOpen(false), 150);
          }
        }}
      >
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="size-[14px] text-gray-400 group-hover:text-gray-600">
          <path d="M4.5 9.5C3.125 9.5 2 10.625 2 12C2 13.375 3.125 14.5 4.5 14.5C5.875 14.5 7 13.375 7 12C7 10.625 5.875 9.5 4.5 9.5Z" fill="currentColor"></path>
          <path d="M19.5 9.5C18.125 9.5 17 10.625 17 12C17 13.375 18.125 14.5 19.5 14.5C20.875 14.5 22 13.375 22 12C22 10.625 20.875 9.5 19.5 9.5Z" fill="currentColor"></path>
          <path d="M9.5 12C9.5 10.625 10.625 9.5 12 9.5C13.375 9.5 14.5 10.625 14.5 12C14.5 13.375 13.375 14.5 12 14.5C10.625 14.5 9.5 13.375 9.5 12Z" fill="currentColor"></path>
        </svg>
      </button>

      {/* Dropdown menu */}
      {isOpen && (
        <div className="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 min-w-[140px]">
          {menuItems.map((item, index) => (
            <div key={index} className="relative">
              <button
                className={`w-full px-3 py-2 text-left hover:bg-gray-50 text-sm flex items-center justify-between ${
                  item.danger ? 'text-red-600 hover:bg-red-50' : 'text-black'
                }`}
                onClick={() => {
                  if (!item.isDate && !item.isStatus) {
                    item.action();
                    if (!item.danger) setIsOpen(false);
                  }
                }}
                onMouseEnter={item.isDate ? handleDateHover : item.isStatus ? handleStatusHover : undefined}
              >
                <span>{item.label}</span>
                {item.label === 'Date' && (
                  <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                )}
                {item.label === 'Status' && (
                  <span className="text-gray-400 text-sm font-medium">&gt;</span>
                )}
              </button>

              {/* Calendar for Date item */}
              {item.isDate && showDatePicker && (
                <div ref={datePickerRef}>
                  <Calendar
                    onDateSelect={handleDateSelect}
                    onClose={() => setShowDatePicker(false)}
                    disabled={disabled}
                  />
                </div>
              )}

              {/* Status Picker for Status item */}
              {item.isStatus && showStatusPicker && (
                <div
                  ref={statusPickerRef}
                  className="absolute left-full top-0 ml-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 min-w-[120px]"
                >
                  <div className="py-1">
                    {[
                      { value: 'default', label: 'Default' },
                      { value: 'delivered', label: 'Delivered' },
                      { value: 'failed', label: 'Not Delivered' },
                      { value: 'read', label: 'Read' }
                    ].map((status) => (
                      <button
                        key={status.value}
                        className="w-full px-3 py-2 text-left hover:bg-gray-50 text-sm text-black flex items-center justify-between"
                        onClick={() => handleStatusSelect(status.value as 'default' | 'delivered' | 'failed' | 'read')}
                        disabled={disabled}
                      >
                        <span>{status.label}</span>
                        {messageStatus === status.value && (
                          <svg className="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Message Row Component
const MessageRow = ({
  message,
  onUpdate,
  onDelete,
  onSetTimestamp,
  onSetStatus,
  disabled = false,
  maxLength = 500
}: {
  message: DraftMessage;
  onUpdate: (id: string, updates: Partial<DraftMessage>) => void;
  onDelete: (id: string) => void;
  onSetTimestamp: (id: string) => void;
  onSetStatus: (id: string) => void;
  disabled?: boolean;
  maxLength?: number;
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const notification = useNotification();

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  }, [message.content]);

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // 验证文件
    const validation = fileValidator.validateFile(file, 10); // 10MB限制
    if (!validation.valid && validation.error) {
      const errorDetails = createUserFriendlyError(validation.error.message, 'Image Upload');
      notification.toast.error(errorDetails.message);

      // 清空文件输入框
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      return;
    }

    // 文件验证通过，开始读取
    const reader = new FileReader();

    reader.onload = (e) => {
      onUpdate(message.id, {
        imageFile: file,
        imageUrl: e.target?.result as string,
        method: 'image'
      });

      // 无障碍公告
      accessibilityManager.announce(`Image uploaded successfully: ${file.name}`, 'polite');
    };

    reader.onerror = () => {
      const errorDetails = createUserFriendlyError('Failed to read image file', 'Image Upload');
      notification.toast.error(errorDetails.message);

      // 清空文件输入框
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    };

    reader.readAsDataURL(file);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      // For real-time preview, we don't need to do anything special on Enter
      // The content is already synced in real-time
    }
  };

  return (
    <div className="shadow-xs relative flex items-center rounded-md px-1 ring ring-gray-200">
      {/* Role Indicator */}
      <RoleIndicator
        actor={message.actor}
        onActorChange={(actor) => onUpdate(message.id, { actor })}
        disabled={disabled}
      />

      {/* Image Upload Button with Preview */}
      <div className="relative">
        <button
          className="focus-visible:ring-accent-600 group flex h-7 w-7 shrink-0 cursor-pointer items-center justify-center focus-visible:rounded-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-inset disabled:opacity-50 disabled:cursor-not-allowed overflow-hidden rounded-sm transition-all duration-200 hover:bg-gray-50"
          type="button"
          disabled={disabled}
          onClick={() => fileInputRef.current?.click()}
        >
          {message.imageUrl ? (
            // 显示图片预览 - 确保上下居中
            <div className="w-full h-full flex items-center justify-center">
              <img
                src={message.imageUrl}
                alt="Image preview"
                className="w-full h-full object-cover object-center rounded-sm"
              />
            </div>
          ) : (
            // 显示默认图片图标
            <svg
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="size-3.5 text-gray-400 group-hover:text-gray-600 transition-colors duration-200"
            >
              <path d="M3 18L6.77044 14.2296C7.55197 13.448 7.94273 13.0573 8.39428 12.9083C8.79155 12.7773 9.21991 12.7741 9.61907 12.8993C10.0728 13.0415 10.4693 13.4265 11.2623 14.1964L14.4861 17.3264M21.5 17.6875L20.0752 16.2627C19.2832 15.4707 18.8872 15.0747 18.4305 14.9263C18.0289 14.7958 17.5962 14.7958 17.1945 14.9263C16.7378 15.0747 16.3416 15.4709 15.5493 16.2633C14.9448 16.8677 14.4861 17.3264 14.4861 17.3264M18.5 21.3403L14.4861 17.3264M18 8C18 9.10457 17.1046 10 16 10C14.8954 10 14 9.10457 14 8C14 6.89543 14.8954 6 16 6C17.1046 6 18 6.89543 18 8ZM11.6 22H12.4C15.7603 22 17.4405 22 18.7239 21.346C19.8529 20.7708 20.7708 19.8529 21.346 18.7239C22 17.4405 22 15.7603 22 12.4V11.6C22 8.23969 22 6.55953 21.346 5.27606C20.7708 4.14708 19.8529 3.2292 18.7239 2.65396C17.4405 2 15.7603 2 12.4 2H11.6C8.23969 2 6.55953 2 5.27606 2.65396C4.14708 3.2292 3.2292 4.14708 2.65396 5.27606C2 6.55953 2 8.23969 2 11.6V12.4C2 15.7603 2 17.4405 2.65396 18.7239C3.2292 19.8529 4.14708 20.7708 5.27606 21.346C6.55953 22 8.23969 22 11.6 22Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
            </svg>
          )}
          <input
            ref={fileInputRef}
            accept="image/*"
            className="hidden"
            type="file"
            onChange={handleImageUpload}
            disabled={disabled}
          />
        </button>

        {/* 删除图片按钮 - 精确定位在预览图片右上角 */}
        {message.imageUrl && (
          <button
            className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 transition-colors duration-200 z-10 shadow-sm"
            onClick={(e) => {
              e.stopPropagation();
              // 清除图片相关状态，恢复到默认状态
              onUpdate(message.id, {
                imageUrl: undefined,
                imageFile: undefined,
                method: 'data'
              });
              // 重置文件输入框
              if (fileInputRef.current) {
                fileInputRef.current.value = '';
              }
            }}
            disabled={disabled}
            title="删除图片"
            aria-label="删除图片"
          >
            ×
          </button>
        )}
      </div>

      {/* Textarea */}
      <textarea
        ref={textareaRef}
        className="field-sizing-content focus-visible:ring-accent-600 min-h-7 sm:min-h-8 w-full resize-none p-2 sm:p-3 text-sm sm:text-base text-black placeholder:text-gray-400 focus-visible:rounded-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-inset disabled:opacity-50 disabled:cursor-not-allowed"
        placeholder="Type a message..."
        value={message.content}
        onChange={(e) => onUpdate(message.id, { content: e.target.value })}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        maxLength={maxLength}
        aria-label={`Message content for ${ROLE_CONFIG[message.actor].label}`}
        aria-describedby={`char-count-${message.id}`}
      />

      {/* Character count - hidden but accessible to screen readers */}
      <div
        id={`char-count-${message.id}`}
        className="sr-only"
        aria-live="polite"
      >
        {message.content.length} of {maxLength} characters used
      </div>

      {/* Three Dots Menu */}
      <MessageMenu
        messageId={message.id}
        messageActor={message.actor}
        messageStatus={message.status}
        onDelete={onDelete}
        onSetTimestamp={onSetTimestamp}
        onSetStatus={onSetStatus}
        onUpdate={onUpdate}
        disabled={disabled}
      />
    </div>
  );
};

// Helper function to convert EnhancedMessage to DraftMessage
const convertToDraftMessage = (message: Message): DraftMessage => {
  // Find the actor based on sender
  let actor: keyof typeof ROLE_CONFIG = 'sender';
  for (const [key, config] of Object.entries(ROLE_CONFIG)) {
    if (config.mappedSender === message.sender) {
      actor = key as keyof typeof ROLE_CONFIG;
      break;
    }
  }

  return {
    id: message.id,
    actor: actor as 'sender' | 'recipient' | 'system',
    content: message.content,
    timestamp: message.timestamp,
    status: message.status || 'default',
    imageUrl: message.imageUrl,
    method: message.method || 'data'
  };
};

// Main MessageInput Component
const MessageInput = forwardRef<MessageInputRef, MessageInputProps>(({
  value = '', // Backward compatibility
  onChange = () => {}, // Backward compatibility
  onAddUser,
  onAddRecipient,
  placeholder = "Type your message here...",
  maxLength = 500,
  disabled = false,
  messages = [],
  onMessagesChange
}: MessageInputProps, ref) => {
  // Initialize draft messages from props or with empty message
  const initializeDraftMessages = useCallback(() => {
    if (messages && messages.length > 0) {
      return messages.map(convertToDraftMessage);
    }
    return [{
      id: 'initial-message-1',
      actor: 'sender' as const,
      content: '',
      method: 'data' as const,
      status: 'default' as const
    }];
  }, [messages]);

  const [draftMessages, setDraftMessages] = useState<DraftMessage[]>(() => initializeDraftMessages());

  const notification = useNotification();

  // Use ref to track initialization state
  const isInitializedRef = useRef<boolean>(false);
  const [isClient, setIsClient] = useState(false);

  // Ensure client-side hydration consistency
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Generate unique ID for new messages - using useRef to ensure consistency between server and client
  const idCounterRef = useRef(0);
  const generateId = useCallback(() => {
    idCounterRef.current += 1;
    // Use a stable ID format that doesn't change between server and client
    return isClient ? `msg-${idCounterRef.current}-${Date.now()}` : `msg-${idCounterRef.current}-ssr`;
  }, [isClient]);

  // Reset messages function
  const resetMessages = useCallback(() => {
    setDraftMessages([{
      id: generateId(),
      actor: 'sender',
      content: '',
      method: 'data',
      status: 'default'
    }]);
    // Reset the initialization flag so the component can be re-initialized
    isInitializedRef.current = false;
  }, [generateId]);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    resetMessages
  }), [resetMessages]);

  // Convert draft messages to messages for real-time preview
  const previewMessages = useMemo(() => {
    return draftMessages
      .filter(draft => draft.content.trim() || draft.imageUrl) // Show messages with content OR images
      .map((draft: DraftMessage): Message => ({
        id: draft.id,
        sender: ROLE_CONFIG[draft.actor].mappedSender,
        content: draft.content,
        timestamp: draft.timestamp,
        status: draft.status,
        imageUrl: draft.imageUrl,
        method: draft.method
      }));
  }, [draftMessages]);

  // Use ref to track the last sent messages to avoid unnecessary updates
  const lastSentMessagesRef = useRef<string>('');

  // Handle external messages changes (from parent component)
  useEffect(() => {
    // Only sync external messages if we haven't been initialized yet
    // and there are actual messages to sync
    if (!isInitializedRef.current && messages && messages.length > 0) {
      const newDraftMessages = messages.map(convertToDraftMessage);
      setDraftMessages(newDraftMessages);
      isInitializedRef.current = true;
    } else if (!isInitializedRef.current) {
      // Mark as initialized even if no external messages
      isInitializedRef.current = true;
    }
  }, [messages]);

  // Real-time sync to parent component
  useEffect(() => {
    if (onMessagesChange && isInitializedRef.current) {
      const messagesString = JSON.stringify(previewMessages);
      if (messagesString !== lastSentMessagesRef.current) {
        lastSentMessagesRef.current = messagesString;
        onMessagesChange(previewMessages);
      }
    }
  }, [previewMessages, onMessagesChange]);

  // Update draft message
  const updateDraftMessage = useCallback((id: string, updates: Partial<DraftMessage>) => {
    setDraftMessages(prev => prev.map(msg =>
      msg.id === id ? { ...msg, ...updates } : msg
    ));
  }, []);

  // Add new message row
  const addMessageRow = useCallback(() => {
    const newMessage: DraftMessage = {
      id: generateId(),
      actor: 'sender',
      content: '',
      method: 'data',
      status: 'default'
    };
    setDraftMessages(prev => [...prev, newMessage]);
  }, [generateId]);

  // Delete message row
  const deleteMessageRow = useCallback((id: string) => {
    setDraftMessages(prev => {
      const filtered = prev.filter(msg => msg.id !== id);
      // Always keep at least one row
      return filtered.length === 0 ? [{
        id: generateId(),
        actor: 'sender',
        content: '',
        method: 'data',
        status: 'default'
      }] : filtered;
    });
  }, [generateId]);



  // Set timestamp for message
  const setTimestamp = useCallback(async (id: string, date?: string) => {
    if (date) {
      // Date provided from date picker
      if (date === '') {
        // Clear timestamp
        updateDraftMessage(id, { timestamp: undefined });
      } else {
        try {
          // Set timestamp to selected date
          const selectedDate = new Date(date);

          // 验证日期是否有效
          if (isNaN(selectedDate.getTime())) {
            console.error('Invalid date string:', date);
            notification.toast.error('Invalid date format');
            return;
          }

          updateDraftMessage(id, { timestamp: selectedDate.toISOString() });
        } catch (error) {
          console.error('Error parsing date:', date, error);
          notification.toast.error('Failed to set timestamp');
        }
      }
    } else {
      // Fallback to modal input (legacy behavior)
      const timestamp = await notification.modal.confirm(
        'Enter custom timestamp for this message:',
        { title: 'Set Timestamp' }
      );
      if (timestamp) {
        updateDraftMessage(id, { timestamp: new Date().toISOString() });
      }
    }
  }, [notification, updateDraftMessage]);

  // Set status for message
  const setStatus = useCallback(async (id: string) => {
    // This would open a status selection modal
    notification.toast.info('Status selection feature coming soon');
  }, [notification]);

  // Legacy compatibility handlers (now just placeholders since we have real-time sync)
  const handleAddMessage = useCallback((actor: 'sender' | 'recipient') => {
    // With real-time preview, this is no longer needed
    // Messages are automatically synced as user types
    notification.toast.info('Messages are now synced in real-time!');
  }, [notification]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        if (e.key === 'Enter') {
          e.preventDefault();
          addMessageRow();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [addMessageRow]);

  return (
    <div className="flex w-full flex-col items-start gap-3">
      {/* Usage instructions */}
      <div className="w-full p-3 bg-green-50 border border-green-200 rounded-lg">
        <div className="flex items-start gap-2">
          <svg className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          <div className="text-sm text-green-700">
            <p className="font-medium mb-1">✨ Real-time Preview Active!</p>
            <ul className="text-xs space-y-1">
              <li>• <strong>Type anywhere</strong> - your messages appear instantly in the iPhone preview</li>
              <li>• <strong>Click colored dots</strong> to switch sender (blue=user, gray=recipient, green=system)</li>
              <li>• <strong>Edit anytime</strong> - changes sync immediately to the preview</li>
              <li>• <strong>Use</strong> <kbd className="px-1 py-0.5 bg-green-100 rounded text-xs">Shift+Enter</kbd> for line breaks</li>
              <li>• <strong>Add images</strong> and use three-dot menu for more options</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Message rows */}
      <div className="flex w-full flex-col gap-2">
        {draftMessages.map((message) => (
          <MessageRow
            key={message.id}
            message={message}
            onUpdate={updateDraftMessage}
            onDelete={deleteMessageRow}
            onSetTimestamp={setTimestamp}
            onSetStatus={setStatus}
            disabled={disabled}
            maxLength={maxLength}
          />
        ))}
      </div>

      {/* Add Message Button */}
      <button
        className="relative isolate inline-flex cursor-pointer items-center justify-center whitespace-nowrap border border-transparent font-semibold focus-visible:border-focus-600 focus-visible:ring-focus-600 focus-visible:outline-none focus-visible:ring disabled:pointer-events-none h-7.5 gap-2 px-3 text-sm bg-blue-500 hover:bg-blue-600 disabled:bg-gray-100 disabled:text-gray-300 text-white rounded-full disabled:opacity-50 disabled:cursor-not-allowed"
        data-accent-color="blue"
        type="button"
        onClick={addMessageRow}
        disabled={disabled}
      >
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="size-3.5">
          <path d="M5 12H12M19 12H12M12 12V5M12 12V19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
        </svg>
        Add Message
      </button>

      {/* Legacy compatibility: Hidden action buttons for backward compatibility */}
      <div className="hidden">
        <button onClick={() => handleAddMessage('sender')}>Add as User</button>
        <button onClick={() => handleAddMessage('recipient')}>Add as Recipient</button>
      </div>
    </div>
  );
});

MessageInput.displayName = 'MessageInput';

export default MessageInput;
