'use client';

import React, { useState, useEffect } from 'react';
import { useNotificationContext } from './NotificationProvider';
import BaseNotification from './BaseNotification';
import Modal from './Modal';
import { AnimationState } from './types';

// Individual notification renderer with animation state management
function NotificationItem({ config, onClose }: { config: any; onClose: (id: string) => void }) {
  const [animationState, setAnimationState] = useState<AnimationState>('entering');

  useEffect(() => {
    // Start entering animation
    const enterTimer = setTimeout(() => {
      setAnimationState('entered');
    }, 50);

    return () => clearTimeout(enterTimer);
  }, []);

  const handleClose = (id: string) => {
    setAnimationState('exiting');
    
    // Wait for exit animation to complete before removing
    setTimeout(() => {
      onClose(id);
    }, 300);
  };

  if (config.type === 'modal') {
    return (
      <Modal
        config={config}
        onClose={handleClose}
        animationState={animationState}
      />
    );
  }

  return (
    <BaseNotification
      config={config}
      onClose={handleClose}
      animationState={animationState}
    />
  );
}

// Main notification renderer component
export function NotificationRenderer() {
  const { state, hide } = useNotificationContext();

  return (
    <>
      {state.notifications.map((notification) => (
        <NotificationItem
          key={notification.id}
          config={notification}
          onClose={hide}
        />
      ))}
    </>
  );
}

export default NotificationRenderer;
