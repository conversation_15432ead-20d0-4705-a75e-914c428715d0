import type { Metada<PERSON> } from "next";
import Link from "next/link";
import Header from "../../components/layout/Header";
import Footer from "../../components/layout/Footer";

export const metadata: Metadata = {
  title: "Privacy Policy - Fake Text Message Generator",
  description: "Our privacy policy explains how we protect your data. We process everything locally and don't collect or store any personal information.",
  keywords: [
    "privacy policy",
    "data protection",
    "GDPR",
    "CCPA",
    "fake text generator privacy",
    "local processing",
    "no data collection"
  ],
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    title: "Privacy Policy - Fake Text Message Generator",
    description: "Our privacy policy explains how we protect your data. We process everything locally and don't collect or store any personal information.",
    type: "website",
  },
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://faketextmessage.xyz'}/privacy-policy`,
  },
};

export default function PrivacyPolicyPage() {
  const lastUpdated = "January 14, 2025";

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Breadcrumb */}
        <nav className="mb-8" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-2 text-sm text-gray-500">
            <li>
              <Link href="/" className="hover:text-blue-600 transition-colors">
                Home
              </Link>
            </li>
            <li className="flex items-center">
              <svg className="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              <span className="text-gray-900">Privacy Policy</span>
            </li>
          </ol>
        </nav>

        {/* Header */}
        <div className="mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Privacy Policy
          </h1>
          <p className="text-lg text-gray-600 mb-2">
            Your privacy is important to us. This policy explains how we handle your data.
          </p>
          <p className="text-sm text-gray-500">
            Last updated: {lastUpdated}
          </p>
        </div>

        {/* Content */}
        <div className="prose prose-lg max-w-none">
          {/* Summary */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-blue-900 mb-3 mt-0">
              🔒 Privacy Summary
            </h2>
            <ul className="text-blue-800 mb-0 space-y-2">
              <li><strong>No Personal Data Collection:</strong> We don't collect, store, or process any personal information.</li>
              <li><strong>Local Processing:</strong> All text generation happens in your browser locally.</li>
              <li><strong>No Server Storage:</strong> Your messages and images are never uploaded to our servers.</li>
              <li><strong>Anonymous Analytics:</strong> We only collect anonymous usage statistics via Google Analytics.</li>
            </ul>
          </div>

          {/* Information We Don't Collect */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Information We Don't Collect
            </h2>
            <p className="text-gray-700 mb-4">
              Unlike many web applications, our Fake Text Message Generator is designed with privacy-first principles:
            </p>
            <ul className="list-disc pl-6 space-y-2 text-gray-700">
              <li>We do not collect names, email addresses, or contact information</li>
              <li>We do not store the text messages you create</li>
              <li>We do not save or upload any images you use</li>
              <li>We do not track your device information or location</li>
              <li>We do not use cookies for tracking or identification</li>
              <li>We do not require user accounts or registration</li>
            </ul>
          </section>

          {/* How Our Service Works */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              How Our Service Works
            </h2>
            <p className="text-gray-700 mb-4">
              Our application operates entirely in your web browser:
            </p>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
              <ol className="list-decimal pl-6 space-y-3 text-gray-700">
                <li><strong>Local Processing:</strong> All text generation and image creation happens on your device</li>
                <li><strong>Browser Storage:</strong> Settings are saved locally in your browser's storage</li>
                <li><strong>No Uploads:</strong> Images and messages never leave your device</li>
                <li><strong>Instant Download:</strong> Generated images are created and downloaded directly from your browser</li>
              </ol>
            </div>
          </section>

          {/* Analytics Information */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Anonymous Analytics
            </h2>
            <p className="text-gray-700 mb-4">
              We use Google Analytics 4 to understand how our service is used, but only collect anonymous data:
            </p>
            <ul className="list-disc pl-6 space-y-2 text-gray-700 mb-4">
              <li>Page views and session duration</li>
              <li>General geographic region (country/state level)</li>
              <li>Device type and browser information</li>
              <li>Referral sources (how you found our site)</li>
            </ul>
            <p className="text-gray-700">
              This data is aggregated and cannot be used to identify individual users. You can opt out of analytics by enabling "Do Not Track" in your browser or using ad blockers.
            </p>
          </section>

          {/* Cookies */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Cookies and Local Storage
            </h2>
            <p className="text-gray-700 mb-4">
              We use minimal data storage for functionality:
            </p>
            <ul className="list-disc pl-6 space-y-2 text-gray-700">
              <li><strong>Essential Cookies:</strong> Required for basic site functionality</li>
              <li><strong>Analytics Cookies:</strong> Google Analytics for anonymous usage statistics</li>
              <li><strong>Local Storage:</strong> Your preferences (theme, settings) saved in your browser</li>
            </ul>
            <p className="text-gray-700 mt-4">
              You can clear this data anytime through your browser settings.
            </p>
          </section>

          {/* Third-Party Services */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Third-Party Services
            </h2>
            <p className="text-gray-700 mb-4">
              We use these external services:
            </p>
            <ul className="list-disc pl-6 space-y-2 text-gray-700">
              <li><strong>Google Analytics:</strong> Anonymous usage statistics (privacy-compliant configuration)</li>
              <li><strong>Vercel:</strong> Website hosting and content delivery</li>
              <li><strong>Google Fonts:</strong> Typography (loaded with privacy-first settings)</li>
            </ul>
            <p className="text-gray-700 mt-4">
              These services have their own privacy policies and operate independently of our application.
            </p>
          </section>

          {/* Data Security */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Data Security
            </h2>
            <p className="text-gray-700 mb-4">
              Since we don't collect personal data, there's minimal security risk:
            </p>
            <ul className="list-disc pl-6 space-y-2 text-gray-700">
              <li>All processing happens locally on your device</li>
              <li>HTTPS encryption for all communications</li>
              <li>No user databases or personal information storage</li>
              <li>Regular security updates for our hosting infrastructure</li>
            </ul>
          </section>

          {/* Your Rights */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Your Rights (GDPR/CCPA Compliance)
            </h2>
            <p className="text-gray-700 mb-4">
              Under GDPR and CCPA, you have certain rights. However, since we don't collect personal data:
            </p>
            <ul className="list-disc pl-6 space-y-2 text-gray-700">
              <li><strong>Right to Access:</strong> No personal data to access</li>
              <li><strong>Right to Deletion:</strong> No personal data to delete</li>
              <li><strong>Right to Portability:</strong> No personal data to export</li>
              <li><strong>Right to Opt-out:</strong> You can disable analytics in your browser</li>
            </ul>
          </section>

          {/* Children's Privacy */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Children's Privacy
            </h2>
            <p className="text-gray-700">
              Our service is safe for all ages as we don't collect any personal information. However, we recommend parental supervision for children under 13 to ensure appropriate use of the text generation features.
            </p>
          </section>

          {/* Changes to Policy */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Changes to This Policy
            </h2>
            <p className="text-gray-700">
              We may update this privacy policy occasionally. Any changes will be posted on this page with an updated "Last modified" date. Since we don't collect contact information, we cannot notify users directly of changes.
            </p>
          </section>

          {/* Contact */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Contact Us
            </h2>
            <p className="text-gray-700 mb-4">
              If you have questions about this privacy policy, please contact us:
            </p>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
              <ul className="space-y-2 text-gray-700">
                <li><strong>Email:</strong> <EMAIL></li>
                <li><strong>Contact Page:</strong> <Link href="/contact" className="text-blue-600 hover:text-blue-800">faketextmessage.xyz/contact</Link></li>
              </ul>
            </div>
          </section>
        </div>

        {/* Navigation */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <Link 
              href="/terms-of-service"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              View Terms of Service
            </Link>
            <Link 
              href="/"
              className="inline-flex items-center text-gray-600 hover:text-gray-800 transition-colors"
            >
              Back to Generator
              <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
