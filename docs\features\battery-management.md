# 智能电量管理系统

## 概述

假短信生成器项目集成了一套完整的电量管理系统，提供真实的 iPhone 电池状态模拟、智能电量验证和可视化显示功能。该系统包括电量百分比设置、BatteryIcon 组件和电量状态同步机制。

## 核心功能

### 🔋 电量百分比设置

#### 可配置范围

电量管理系统支持 0-100% 的完整电量范围设置：

```typescript
// 电量状态管理
const [batteryPercentage, setBatteryPercentage] = useState<number>(85);
const [batteryError, setBatteryError] = useState<string>('');

// 电量输入处理函数
const handleBatteryChange = useCallback((value: string) => {
  // 只允许数字输入
  const numericValue = value.replace(/[^0-9]/g, '');

  if (numericValue === '') {
    setBatteryPercentage(0);
    setBatteryError('');
    return;
  }

  const percentage = parseInt(numericValue, 10);

  // 验证范围 (0-100)
  if (percentage < 0 || percentage > 100) {
    setBatteryError('Battery percentage must be between 0 and 100');
    return;
  }

  setBatteryPercentage(percentage);
  setBatteryError('');
}, []);
```

#### 输入验证机制

```typescript
// 数字输入过滤
const numericValue = value.replace(/[^0-9]/g, '');

// 边界值验证
const isValidPercentage = (value: number): boolean => {
  return value >= 0 && value <= 100;
};

// 错误提示生成
const getErrorMessage = (value: number): string => {
  if (value < 0) return 'Battery percentage cannot be negative';
  if (value > 100) return 'Battery percentage cannot exceed 100%';
  return '';
};
```

### ⚡ BatteryIcon 组件系统

#### 组件特性

BatteryIcon 组件提供真实的 iPhone 电池图标渲染：

```typescript
interface BatteryIconProps {
  percentage?: number;    // 电量百分比 (0-100)，默认85
  charging?: boolean;     // 是否显示充电状态，默认false
  className?: string;     // 自定义CSS类名
  color?: string;         // 图标颜色，默认'currentColor'
  mode?: 'light' | 'dark'; // 主题模式，默认'light'
}
```

#### 电量可视化算法

```typescript
// 计算电池填充宽度
const fillWidth = Math.max(0, Math.min(100, percentage));
const fillColor = percentage > 20 ? color : '#FF3B30'; // 低电量时显示红色

// 电量填充渲染
<div 
  className="absolute top-[2.5px] left-[2.2px] h-[6px] transition-all duration-300 rounded-[1px]"
  style={{ 
    backgroundColor: fillColor,
    width: `${(fillWidth / 100) * 15.5}px`, // 根据SVG内部空间调整
  }}
></div>
```

#### 低电量警告系统

```typescript
// 低电量警告逻辑
const getBatteryColor = (percentage: number, defaultColor: string): string => {
  if (percentage <= 20) {
    return '#FF3B30'; // iPhone 红色警告色
  }
  return defaultColor;
};

// 低电量状态判断
const isLowBattery = (percentage: number): boolean => {
  return percentage <= 20;
};

// 电量状态分类
enum BatteryStatus {
  CRITICAL = 'critical',  // 0-5%
  LOW = 'low',           // 6-20%
  NORMAL = 'normal',     // 21-100%
}

const getBatteryStatus = (percentage: number): BatteryStatus => {
  if (percentage <= 5) return BatteryStatus.CRITICAL;
  if (percentage <= 20) return BatteryStatus.LOW;
  return BatteryStatus.NORMAL;
};
```

### 🎨 真实 iPhone 电池图标

#### SVG 设计

基于真实 iPhone 电池图标的 SVG 实现：

```typescript
// 电池主体SVG（基于真实iPhone设计）
<svg viewBox="0 0 29 13" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-[11px]">
  <path 
    d="M4 2C1.79086 2 0 3.79086 0 6V7C0 9.20914 1.79086 11 4 11H21C23.2091 11 25 9.20914 25 7V6C25 3.79086 23.2091 2 21 2H4Z" 
    fill="none" 
    stroke="currentColor" 
    strokeWidth="1"
  />
  <path 
    d="M26 4.5C26 4.22386 26.2239 4 26.5 4H27.5C27.7761 4 28 4.22386 28 4.5V8.5C28 8.77614 27.7761 9 27.5 9H26.5C26.2239 9 26 8.77614 26 8.5V4.5Z" 
    fill="currentColor"
  />
</svg>
```

#### 动画效果

```typescript
// CSS 过渡动画
.transition-all.duration-300 {
  transition-property: all;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

// 电量变化动画
const animateBatteryChange = (fromPercentage: number, toPercentage: number) => {
  // 平滑过渡到新的电量值
  const duration = 300; // 毫秒
  const steps = 30;
  const stepValue = (toPercentage - fromPercentage) / steps;
  
  let currentStep = 0;
  const interval = setInterval(() => {
    currentStep++;
    const currentPercentage = fromPercentage + (stepValue * currentStep);
    
    if (currentStep >= steps) {
      clearInterval(interval);
      setBatteryPercentage(toPercentage);
    } else {
      setBatteryPercentage(Math.round(currentPercentage));
    }
  }, duration / steps);
};
```

### 🔄 电量状态同步

#### 实时同步机制

```typescript
// 电量状态在组件间的同步
useEffect(() => {
  // 电量变化时同步到所有相关组件
  if (phonePreviewRef.current) {
    phonePreviewRef.current.updateBatteryLevel(batteryPercentage);
  }
}, [batteryPercentage]);

// PhonePreview 组件中的电量接收
const PhonePreview = forwardRef<HTMLDivElement, PhonePreviewProps>(({
  batteryPercentage = 85, // 默认电量85%
  // ... 其他属性
}, ref) => {
  return (
    <div ref={ref}>
      {/* 状态栏中的电池图标 */}
      <BatteryIcon
        percentage={batteryPercentage}
        mode={mode}
        color="currentColor"
        className="h-[11px]"
      />
    </div>
  );
});
```

#### 重置功能

```typescript
// 电量重置到默认值
const resetBattery = () => {
  setBatteryPercentage(85); // 重置电量为默认值85%
  setBatteryError(''); // 清除电量错误信息
};

// 在整体重置功能中包含电量重置
const handleReset = () => {
  setRecipientName('');
  setRecipientAvatar(null);
  setMessages([]);
  resetTime();
  setMode('light');
  setBatteryPercentage(85); // 重置电量为默认值85%
  setBatteryError(''); // 清除电量错误信息
};
```

## 使用场景

### 主界面电量设置

```typescript
// 在主界面的设备配置区域
<div>
  <label className="block text-sm font-medium text-gray-700 mb-2">Battery Level</label>
  <input
    type="text"
    value={batteryPercentage}
    onChange={(e) => handleBatteryChange(e.target.value)}
    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:border-transparent text-black transition-colors ${
      batteryError
        ? 'border-red-300 focus:ring-red-500 bg-red-50'
        : 'border-gray-300 focus:ring-blue-500'
    }`}
    placeholder="85"
    maxLength={3}
  />
  <p className="text-xs text-gray-500 mt-1">
    Enter percentage (0-100)
  </p>
  {batteryError && (
    <p className="text-red-500 text-xs mt-1 flex items-center">
      <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
      </svg>
      {batteryError}
    </p>
  )}
</div>
```

### iPhone 状态栏显示

```typescript
// 在 PhonePreview 组件的状态栏中
<div className="flex items-center gap-[5px]">
  <svg viewBox="0 0 27 17" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-[10px]">
    {/* 信号图标 */}
  </svg>
  {/* 电池图标 - 使用可配置的BatteryIcon组件 */}
  <BatteryIcon
    percentage={batteryPercentage}
    mode={mode}
    color="currentColor"
    className="h-[11px]"
  />
</div>
```

## 最佳实践

### 1. 电量范围验证

```typescript
// 确保电量值在有效范围内
const validateBatteryPercentage = (value: number): boolean => {
  return Number.isInteger(value) && value >= 0 && value <= 100;
};

// 安全的电量设置
const setSafeBatteryPercentage = (value: number) => {
  const safeValue = Math.max(0, Math.min(100, Math.round(value)));
  setBatteryPercentage(safeValue);
};
```

### 2. 用户体验优化

```typescript
// 提供预设电量选项
const BATTERY_PRESETS = [
  { label: 'Full (100%)', value: 100 },
  { label: 'High (85%)', value: 85 },
  { label: 'Medium (50%)', value: 50 },
  { label: 'Low (20%)', value: 20 },
  { label: 'Critical (5%)', value: 5 },
];

// 快速设置按钮
const BatteryPresets = () => (
  <div className="flex gap-2 mt-2">
    {BATTERY_PRESETS.map(preset => (
      <button
        key={preset.value}
        onClick={() => setBatteryPercentage(preset.value)}
        className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded"
      >
        {preset.label}
      </button>
    ))}
  </div>
);
```

### 3. 无障碍支持

```typescript
// 添加无障碍属性
<div 
  role="progressbar" 
  aria-valuenow={batteryPercentage} 
  aria-valuemin={0} 
  aria-valuemax={100}
  aria-label={`Battery level: ${batteryPercentage}%`}
>
  <BatteryIcon percentage={batteryPercentage} />
</div>
```

### 4. 性能优化

```typescript
// 使用 useMemo 优化电量计算
const batteryProps = useMemo(() => ({
  percentage: batteryPercentage,
  charging: false,
  mode: themeMode,
  color: 'currentColor'
}), [batteryPercentage, themeMode]);

// 防抖输入处理
const debouncedBatteryChange = useMemo(
  () => debounce(handleBatteryChange, 300),
  []
);
```

## 扩展性

### 添加充电状态

```typescript
// 扩展电量状态类型
interface BatteryState {
  percentage: number;
  charging: boolean;
  chargingSpeed?: 'slow' | 'fast' | 'wireless';
}

// 充电状态管理
const [batteryState, setBatteryState] = useState<BatteryState>({
  percentage: 85,
  charging: false
});
```

### 电量历史记录

```typescript
// 电量变化历史
interface BatteryHistory {
  timestamp: number;
  percentage: number;
  action: 'manual' | 'preset' | 'reset';
}

const [batteryHistory, setBatteryHistory] = useState<BatteryHistory[]>([]);

// 记录电量变化
const recordBatteryChange = (percentage: number, action: string) => {
  setBatteryHistory(prev => [...prev, {
    timestamp: Date.now(),
    percentage,
    action
  }]);
};
```

## 总结

智能电量管理系统为假短信生成器提供了真实的电池状态模拟功能，具有以下优势：

- ✅ 完整的电量范围支持（0-100%）
- ✅ 智能的输入验证和错误处理
- ✅ 真实的 iPhone 电池图标渲染
- ✅ 低电量警告和视觉反馈
- ✅ 平滑的动画过渡效果
- ✅ 优秀的性能和可扩展性

该系统确保了电池状态显示的真实性和用户体验的一致性，为项目的整体质量提供了重要保障。
