/**
 * 轻量级错误处理工具
 * 为个人项目提供简单而有效的错误处理
 */

export interface ErrorInfo {
  code: string;
  message: string;
  userMessage: string;
  suggestions?: readonly string[];
}

/**
 * 常见错误类型定义
 */
export const ERROR_TYPES = {
  // 网络相关错误
  NETWORK_ERROR: {
    code: 'NETWORK_ERROR',
    message: 'Network connection failed',
    userMessage: 'Network connection issue. Please check your internet connection and try again.',
    suggestions: [
      'Check your internet connection',
      'Try refreshing the page',
      'Disable VPN if using one'
    ]
  },
  
  // 文件上传错误
  FILE_TOO_LARGE: {
    code: 'FILE_TOO_LARGE',
    message: 'File size exceeds limit',
    userMessage: 'The image file is too large. Please choose a file smaller than 10MB.',
    suggestions: [
      'Compress your image using online tools',
      'Choose a different image format (JPG is usually smaller)',
      'Resize the image to smaller dimensions'
    ]
  },
  
  FILE_INVALID_FORMAT: {
    code: 'FILE_INVALID_FORMAT',
    message: 'Invalid file format',
    userMessage: 'This file format is not supported. Please use JPG, PNG, GIF, or WebP images.',
    suggestions: [
      'Convert your image to JPG or PNG format',
      'Use image editing software to save in supported format'
    ]
  },
  
  // 下载相关错误
  DOWNLOAD_FAILED: {
    code: 'DOWNLOAD_FAILED',
    message: 'Download process failed',
    userMessage: 'Failed to generate the image. Please try again.',
    suggestions: [
      'Try again in a few seconds',
      'Check if your browser allows downloads',
      'Try using a different browser'
    ]
  },
  
  CANVAS_ERROR: {
    code: 'CANVAS_ERROR',
    message: 'Canvas rendering failed',
    userMessage: 'Unable to generate the image. This might be due to browser limitations.',
    suggestions: [
      'Try using Chrome or Firefox for better compatibility',
      'Disable browser extensions temporarily',
      'Clear browser cache and try again'
    ]
  },
  
  // 浏览器兼容性
  BROWSER_NOT_SUPPORTED: {
    code: 'BROWSER_NOT_SUPPORTED',
    message: 'Browser not supported',
    userMessage: 'Your browser may not support all features. Please use a modern browser.',
    suggestions: [
      'Update your browser to the latest version',
      'Try using Chrome, Firefox, Safari, or Edge',
      'Enable JavaScript if disabled'
    ]
  },
  
  // 通用错误
  UNKNOWN_ERROR: {
    code: 'UNKNOWN_ERROR',
    message: 'Unknown error occurred',
    userMessage: 'Something unexpected happened. Please try again.',
    suggestions: [
      'Refresh the page and try again',
      'Clear browser cache',
      'Try using a different browser'
    ]
  }
} as const;

/**
 * 错误分类器 - 根据错误信息判断错误类型
 */
export function classifyError(error: Error | string): ErrorInfo {
  const errorMessage = typeof error === 'string' ? error : error.message.toLowerCase();
  
  // 网络错误
  if (errorMessage.includes('network') || 
      errorMessage.includes('fetch') || 
      errorMessage.includes('connection') ||
      errorMessage.includes('timeout')) {
    return ERROR_TYPES.NETWORK_ERROR;
  }
  
  // 文件大小错误
  if (errorMessage.includes('file') && 
      (errorMessage.includes('large') || errorMessage.includes('size') || errorMessage.includes('limit'))) {
    return ERROR_TYPES.FILE_TOO_LARGE;
  }
  
  // 文件格式错误
  if (errorMessage.includes('format') || 
      errorMessage.includes('type') || 
      errorMessage.includes('invalid file')) {
    return ERROR_TYPES.FILE_INVALID_FORMAT;
  }
  
  // Canvas相关错误
  if (errorMessage.includes('canvas') || 
      errorMessage.includes('html2canvas') || 
      errorMessage.includes('render')) {
    return ERROR_TYPES.CANVAS_ERROR;
  }
  
  // 下载错误
  if (errorMessage.includes('download') || 
      errorMessage.includes('blob') || 
      errorMessage.includes('save')) {
    return ERROR_TYPES.DOWNLOAD_FAILED;
  }
  
  // 默认未知错误
  return ERROR_TYPES.UNKNOWN_ERROR;
}

/**
 * 文件验证工具
 */
export const fileValidator = {
  /**
   * 检查文件大小
   */
  checkFileSize(file: File, maxSizeMB: number = 10): { valid: boolean; error?: ErrorInfo } {
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      return {
        valid: false,
        error: ERROR_TYPES.FILE_TOO_LARGE
      };
    }
    return { valid: true };
  },
  
  /**
   * 检查文件格式
   */
  checkFileFormat(file: File, allowedTypes: string[] = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']): { valid: boolean; error?: ErrorInfo } {
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: ERROR_TYPES.FILE_INVALID_FORMAT
      };
    }
    return { valid: true };
  },
  
  /**
   * 综合文件验证
   */
  validateFile(file: File, maxSizeMB: number = 10): { valid: boolean; error?: ErrorInfo } {
    // 检查格式
    const formatCheck = this.checkFileFormat(file);
    if (!formatCheck.valid) {
      return formatCheck;
    }
    
    // 检查大小
    const sizeCheck = this.checkFileSize(file, maxSizeMB);
    if (!sizeCheck.valid) {
      return sizeCheck;
    }
    
    return { valid: true };
  }
};

/**
 * 浏览器兼容性检查
 */
export function checkBrowserCompatibility(): { compatible: boolean; issues: string[] } {
  const issues: string[] = [];
  
  // 检查基本API支持
  if (!window.HTMLCanvasElement) {
    issues.push('Canvas API not supported');
  }
  
  if (!window.File || !window.FileReader) {
    issues.push('File API not supported');
  }
  
  if (!window.URL || !window.URL.createObjectURL) {
    issues.push('URL API not supported');
  }
  
  // 检查现代JavaScript特性
  if (!window.Promise) {
    issues.push('Promise not supported');
  }
  
  return {
    compatible: issues.length === 0,
    issues
  };
}

/**
 * 创建用户友好的错误消息
 */
export function createUserFriendlyError(error: Error | string, context?: string): {
  title: string;
  message: string;
  suggestions: readonly string[];
} {
  const errorInfo = classifyError(error);

  return {
    title: getErrorTitle(errorInfo.code, context),
    message: errorInfo.userMessage,
    suggestions: errorInfo.suggestions || []
  };
}

/**
 * 根据错误代码和上下文生成标题
 */
function getErrorTitle(errorCode: string, context?: string): string {
  const contextPrefix = context ? `${context}: ` : '';

  switch (errorCode) {
    case 'NETWORK_ERROR':
      return `${contextPrefix}Connection Problem`;
    case 'FILE_TOO_LARGE':
      return `${contextPrefix}File Too Large`;
    case 'FILE_INVALID_FORMAT':
      return `${contextPrefix}Invalid File Format`;
    case 'DOWNLOAD_FAILED':
      return `${contextPrefix}Download Failed`;
    case 'CANVAS_ERROR':
      return `${contextPrefix}Image Generation Failed`;
    case 'BROWSER_NOT_SUPPORTED':
      return `${contextPrefix}Browser Compatibility Issue`;
    default:
      return `${contextPrefix}Something Went Wrong`;
  }
}

/**
 * 网络状态检查工具
 */
export const networkUtils = {
  /**
   * 检查网络连接状态
   */
  isOnline(): boolean {
    return navigator.onLine;
  },

  /**
   * 监听网络状态变化
   */
  onNetworkChange(callback: (isOnline: boolean) => void): () => void {
    const handleOnline = () => callback(true);
    const handleOffline = () => callback(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // 返回清理函数
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  },

  /**
   * 简单的网络连接测试
   */
  async testConnection(timeout: number = 5000): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch('/favicon.ico', {
        method: 'HEAD',
        cache: 'no-cache',
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch {
      return false;
    }
  }
};
