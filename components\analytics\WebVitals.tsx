'use client';

import { useReportWebVitals } from 'next/web-vitals';

/**
 * Web Vitals监控组件
 * 
 * 功能：
 * - 监控Core Web Vitals指标
 * - 发送数据到Google Analytics
 * - 支持自定义分析端点
 * - 提升SEO排名
 */
export default function WebVitals() {
  useReportWebVitals((metric) => {
    // 发送到Google Analytics（如果可用）
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', metric.name, {
        value: Math.round(
          metric.name === 'CLS' ? metric.value * 1000 : metric.value
        ), // 值必须是整数
        event_label: metric.id, // 当前页面加载的唯一ID
        non_interaction: true, // 避免影响跳出率
        custom_map: {
          metric_id: metric.id,
          metric_value: metric.value,
          metric_delta: metric.delta,
        },
      });
    }

    // 可选：发送到自定义分析端点
    if (process.env.NEXT_PUBLIC_ANALYTICS_ENDPOINT) {
      fetch(process.env.NEXT_PUBLIC_ANALYTICS_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: metric.name,
          value: metric.value,
          id: metric.id,
          delta: metric.delta,
          navigationType: metric.navigationType,
          timestamp: Date.now(),
          url: window.location.href,
          userAgent: navigator.userAgent,
        }),
      }).catch((error) => {
        console.warn('Failed to send web vitals:', error);
      });
    }

    // 开发环境下的控制台输出
    if (process.env.NODE_ENV === 'development') {
      console.log('Web Vital:', {
        name: metric.name,
        value: metric.value,
        id: metric.id,
        delta: metric.delta,
        navigationType: metric.navigationType,
      });
    }
  });

  return null; // 这个组件不渲染任何内容
}
