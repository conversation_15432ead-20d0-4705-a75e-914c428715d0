'use client';

import { useState, useEffect } from 'react';

interface CookieConsentProps {
  onConsentChange?: (consent: boolean) => void;
}

/**
 * GDPR合规的Cookie同意组件
 * 
 * 功能：
 * - 符合GDPR要求的cookie同意管理
 * - 本地存储用户同意状态
 * - 支持同意状态变更回调
 * - Apple风格的简洁设计
 */
export default function CookieConsent({ onConsentChange }: CookieConsentProps) {
  const [showBanner, setShowBanner] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // 检查用户是否已经做出选择
    const consent = localStorage.getItem('cookie-consent');
    if (consent === null) {
      setShowBanner(true);
    } else {
      // 通知父组件当前的同意状态
      onConsentChange?.(consent === 'accepted');
    }
    setIsLoading(false);
  }, [onConsentChange]);

  const handleAccept = () => {
    localStorage.setItem('cookie-consent', 'accepted');
    setShowBanner(false);
    onConsentChange?.(true);
  };

  const handleDecline = () => {
    localStorage.setItem('cookie-consent', 'declined');
    setShowBanner(false);
    onConsentChange?.(false);
  };

  // 不显示加载状态或已经做出选择
  if (isLoading || !showBanner) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex-1">
            <h3 className="text-sm font-medium text-gray-900 mb-1">
              Cookie Settings
            </h3>
            <p className="text-sm text-gray-600">
              We use cookies to analyze website traffic and optimize your experience. 
              By accepting our use of cookies, your data will be aggregated with all other user data.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
            <button
              onClick={handleDecline}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200"
            >
              Decline
            </button>
            <button
              onClick={handleAccept}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-500 rounded-lg hover:bg-blue-600 transition-colors duration-200"
            >
              Accept
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
