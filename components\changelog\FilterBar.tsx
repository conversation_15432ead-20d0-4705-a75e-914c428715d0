import React from 'react';
import { FilterBarProps, FilterType } from '../../types/changelog';

/**
 * Filter component
 * Provides filtering functionality by update type
 */
const FilterBar: React.FC<FilterBarProps> = ({
  activeFilter,
  updateTypes,
  onFilterChange
}) => {
  const filterOptions: { value: FilterType; label: string; count?: number }[] = [
    { value: 'all', label: 'All', count: updateTypes.reduce((sum, type) => sum + type.count, 0) },
    ...updateTypes.map(type => ({
      value: type.type as FilterType,
      label: type.label,
      count: type.count
    }))
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <h3 className="text-sm font-medium text-gray-700 mb-3">
        Filter by Type
      </h3>

      <div className="flex flex-wrap gap-2">
        {filterOptions.map((option) => {
          const isActive = activeFilter === option.value;
          const typeConfig = updateTypes.find(type => type.type === option.value);

          return (
            <button
              key={option.value}
              onClick={() => onFilterChange(option.value)}
              className={`inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                isActive
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
              aria-pressed={isActive}
            >
              {/* Icon (only for specific types) */}
              {typeConfig && (
                <svg
                  className={`w-4 h-4 mr-2 ${isActive ? 'text-white' : typeConfig.color}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d={typeConfig.icon}
                  />
                </svg>
              )}

              {/* Label */}
              <span>{option.label}</span>

              {/* Count */}
              {option.count !== undefined && (
                <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${
                  isActive
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {option.count}
                </span>
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default FilterBar;
