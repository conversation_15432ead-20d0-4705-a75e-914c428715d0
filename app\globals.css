@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary-blue: #3B82F6;
  --primary-blue-hover: #2563EB;

  /* iOS 17/18 System Colors */
  --ios-blue-light: #007AFF;
  --ios-blue-dark: #0A84FF;
  --ios-gray-light: #F2F2F7;
  --ios-gray-dark: #1C1C1E;
  --ios-background-light: #FFFFFF;
  --ios-background-dark: #000000;

  /* iOS Message Bubble Colors - Light Mode */
  --send-bg: #007AFF;
  --send-color: white;
  --receive-bg: #E5E5EA;
  --receive-text: black;
  --system-bg: #34C759;
  --system-color: white;
  --page-background: #FFFFFF;

  --gray-50: #F9FAFB;
  --gray-100: #F3F4F6;
  --gray-200: #E5E7EB;
  --gray-300: #D1D5DB;
  --gray-400: #9CA3AF;
  --gray-500: #6B7280;
  --gray-600: #4B5563;
  --gray-700: #374151;
  --gray-800: #1F2937;
  --gray-900: #111827;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;

    /* iOS Message Bubble Colors - Dark Mode */
    --send-bg: #007AFF;
    --send-color: white;
    --receive-bg: #262628;
    --receive-text: white;
    --system-bg: #34C759;
    --system-color: white;
    --page-background: #000000;
  }
}

/* Dark mode class-based theme support */
.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  --page-background: #000000;
}

/* Apple-style system font stack */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Apple-style smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Apple-style focus states */
*:focus {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* Apple-style button transitions */
button, a {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Apple-style backdrop blur support */
.backdrop-blur-md {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Apple-style shadows */
.shadow-apple {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.shadow-apple-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* iOS Message Bubble Shadows */
.shadow-message-light {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.shadow-message-dark {
  box-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);
}

/* iPhone Device Shadow */
.shadow-device {
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.05),
    0 10px 25px rgba(0, 0, 0, 0.1),
    0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Apple-style rounded corners */
.rounded-apple {
  border-radius: 12px;
}

.rounded-apple-lg {
  border-radius: 16px;
}

/* Mobile optimizations */
@media (max-width: 640px) {
  /* Improve touch targets */
  button, input, select, textarea {
    min-height: 44px;
  }

  /* Reduce padding on small screens */
  .mobile-compact {
    padding: 0.75rem;
  }

  /* Better text sizing */
  .mobile-text {
    font-size: 0.875rem;
  }
}

/* Prevent zoom on input focus (iOS) */
input[type="text"],
input[type="email"],
input[type="number"],
input[type="password"],
input[type="search"],
input[type="tel"],
input[type="url"],
select,
textarea {
  font-size: 16px;
}

@media (max-width: 475px) {
  input[type="text"],
  input[type="email"],
  input[type="number"],
  input[type="password"],
  input[type="search"],
  input[type="tel"],
  input[type="url"],
  select,
  textarea {
    font-size: 16px;
  }
}

/* iOS Messages scrollbar hiding */
.no-scrollbar {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.no-scrollbar::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}
