# 🎭 假短信生成器 (Fake Text Message Generator)

[![Next.js](https://img.shields.io/badge/Next.js-15.3.5-black?style=flat-square&logo=next.js)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-19.0.0-blue?style=flat-square&logo=react)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-4.0-38B2AC?style=flat-square&logo=tailwind-css)](https://tailwindcss.com/)
[![Vercel](https://img.shields.io/badge/Deployed_on-Vercel-black?style=flat-square&logo=vercel)](https://vercel.com/)

> 🌐 **在线演示**: [https://faketextmessage.xyz](https://faketextmessage.xyz)

免费创建逼真的假短信对话界面，完美适用于短视频制作、界面设计稿和创意项目。无需注册，即时下载高清图片。

## ✨ 功能特性

### 🎨 核心功能

- **逼真的 iPhone Messages 界面** - 完全还原 iOS 17/18 Messages 应用设计
  - Dynamic Island 状态栏设计，支持真实的时间显示
  - 真实的消息气泡和尾部效果（CSS 伪元素实现）
  - 12/24小时制时间显示（状态栏无 AM/PM 标识，符合真实 iPhone 界面）
- **实时 WYSIWYG 预览** - 输入即时同步，无需点击确认
  - 所见即所得的编辑体验
  - 实时内容同步到预览区域
  - 无延迟的交互响应
- **多媒体消息支持** - 完整的图片处理功能
  - 支持图片上传（JPEG、PNG、GIF、WebP）
  - 图片预览和与文本的混合显示
  - 智能图片尺寸适配和优化
- **智能角色系统** - 基于颜色的角色指示器
  - 发送者消息（蓝色圆点指示器）
  - 接收者消息（灰色圆点指示器）
  - 系统消息（绿色圆点指示器）
  - 一键角色切换功能
- **高级时间管理** - 完整的时间调整系统
  - 智能时间输入管理 Hook (`useTimeInput`)
  - 12/24小时制格式切换和验证
  - 输入掩码和自动格式化功能
  - 悬停触发的可编辑日期选择器
  - 自定义消息时间戳和状态设置
  - 边界值验证防止无效时间输入
- **智能电量管理** - 真实的电池状态模拟
  - 可调节电量百分比（0-100%）
  - 智能电量验证和错误提示
  - 低电量红色警告显示（≤20%）
  - 真实的 iPhone 电池图标渲染
  - 电量状态实时同步到预览界面
- **交互式教程系统** - 分步骤学习指导
  - 完整的交互式教程页面 (`/tutorial`)
  - 分步骤引导和实时演示
  - 遮罩高亮和聚光灯效果
  - 键盘导航支持（方向键、Enter、Escape）
  - 进度指示和步骤管理
  - Apple 风格设计和平滑动画
- **专业级导出** - 高质量图片下载
  - PNG 格式高清图片导出
  - 保持界面一致性和真实性
  - 优化的截图质量

### 🌙 用户体验

- **Apple 风格界面** - 简洁优雅的设计语言
  - San Francisco 字体系统
  - 圆角设计和柔和过渡动画（300ms）
  - 响应式设计和移动优先方法
- **明暗主题切换** - 完整的主题支持
  - 浅色模式（经典白色背景）
  - 深色模式（真实的 iOS 深色界面）
  - 主题状态持久化保存
- **智能消息管理** - 高效的内容编辑
  - 多行文本输入支持
  - 实时字符计数和限制
  - 拖拽排序和批量操作
- **交互优化** - 流畅的用户操作
  - 悬停效果和视觉反馈
  - 键盘快捷键支持（Shift+Enter 换行）
  - 智能焦点管理

### 🚀 技术特性

- **Next.js 15.3.5** - 最新的 React 框架
  - App Router 架构
  - 服务端渲染和静态生成
  - 自动代码分割和优化
- **TypeScript 5.0** - 完整的类型安全
  - 严格的类型检查
  - 智能代码提示和重构
  - 接口定义和类型推导
- **Tailwind CSS 4.0** - 现代化的原子化 CSS
  - 自定义设计系统
  - 响应式断点管理
  - 深色模式支持
- **高级组件架构** - 模块化设计
  - 自定义 React Hooks
  - 组件组合和复用
  - 状态管理和事件处理
- **SEO 和性能优化** - 企业级配置
  - 动态 sitemap.xml 和 robots.txt
  - 结构化数据标记
  - Google Analytics 4 集成
  - Core Web Vitals 优化

## 🛠️ 技术栈

### 核心框架
- **框架**: [Next.js 15.3.5](https://nextjs.org/) with App Router
- **语言**: [TypeScript 5.0](https://www.typescriptlang.org/)
- **样式**: [Tailwind CSS 4.0](https://tailwindcss.com/)
- **运行时**: [React 19.0.0](https://reactjs.org/)

### UI 和交互
- **UI 组件**: 自定义 React 函数组件
- **状态管理**: React Hooks (useState, useEffect, useCallback, useMemo)
- **时间管理**: 自定义 useTimeInput Hook，完整的时间状态管理
- **电量管理**: 智能电量验证和 BatteryIcon 组件系统
- **通知系统**: 自定义 Toast 组件
- **字体**: San Francisco 风格系统字体
- **图标**: 自定义 SVG 图标组件（信号、WiFi、电池等）

### 图片和媒体处理
- **图片处理**: [html2canvas-pro](https://html2canvas.hertzen.com/)
- **文件上传**: 原生 File API
- **图片优化**: Next.js Image 组件
- **格式支持**: JPEG, PNG, GIF, WebP

### 开发和部署
- **包管理**: npm/yarn
- **代码检查**: ESLint + Prettier
- **类型检查**: TypeScript 严格模式
- **部署**: [Vercel](https://vercel.com/) 免费计划
- **域名**: https://faketextmessage.xyz

### SEO 和分析
- **SEO**: 动态 sitemap.xml, robots.txt, 结构化数据
- **分析**: Google Analytics 4 (G-E5KYVM3REM)
- **性能**: Core Web Vitals 优化
- **合规**: GDPR 兼容的 Cookie 管理

## 🧩 组件使用指南

### 核心组件概览

项目采用模块化设计，主要组件包括：

#### 📱 PhonePreview - iPhone 预览组件
完整的 iPhone 界面模拟组件，支持 Dynamic Island、状态栏、聊天界面等。

```typescript
import PhonePreview from '../components/device/PhonePreview';
import { Message, TimeFormat, ThemeMode } from '../types/message';

const messages: Message[] = [
  {
    id: '1',
    sender: 'user',
    content: '你好！',
    timestamp: '10:30',
    status: 'delivered'
  },
  {
    id: '2',
    sender: 'recipient',
    content: '嗨，最近怎么样？',
    timestamp: '10:31',
    status: 'read'
  }
];

<PhonePreview
  recipientName="张三"
  recipientAvatar="/avatar.jpg"
  messages={messages}
  deviceTime="9:41"
  timeFormat="12"
  mode="light"
  batteryPercentage={85}
/>
```

#### 💬 MessageBubble - 消息气泡组件
iOS 风格的消息气泡，支持文本、图片和混合内容。

```typescript
import MessageBubble from '../components/ui/MessageBubble';

<MessageBubble
  message={{
    id: '1',
    sender: 'user',
    content: '这是一条用户消息',
    imageUrl: '/image.jpg' // 可选
  }}
  recipientName="联系人"
  recipientAvatar="/avatar.jpg"
  mode="light"
  timeFormat="12"
  showTime={true}
/>
```

#### ⌨️ MessageInput - 消息输入组件
多功能消息输入界面，支持角色切换、图片上传、实时预览。

```typescript
import MessageInput from '../components/ui/MessageInput';
import { useState } from 'react';
import { Message } from '../types/message';

const [messages, setMessages] = useState<Message[]>([]);

// 完整功能使用（推荐）
<MessageInput
  messages={messages}
  onMessagesChange={setMessages}
  onAddUser={() => console.log('用户消息已添加')}
  onAddRecipient={() => console.log('接收方消息已添加')}
  placeholder="Type your message here..."
  maxLength={1000}
/>

// 基础使用（向后兼容）
<MessageInput
  onAddUser={() => addMessage('user')}
  onAddRecipient={() => addMessage('recipient')}
  placeholder="输入消息内容..."
  maxLength={500}
  disabled={false}
/>
```

**主要功能**：
- ✅ **实时预览**: WYSIWYG 编辑，输入即时同步
- ✅ **图片上传**: 支持多种格式（JPEG、PNG、GIF、WebP），即时预览
- ✅ **角色切换**: 彩色圆点指示器（蓝色=发送者，灰色=接收者，绿色=系统）
- ✅ **高级时间管理**:
  - 悬停触发的智能日期选择器
  - 可编辑的年月日时分字段
  - 自定义消息时间戳和状态设置
  - 实时时间验证和格式化
- ✅ **消息状态**: 支持已送达、失败、已读状态设置

#### 🏝️ DynamicIsland - 动态岛组件
iPhone 15 系列的 Dynamic Island 状态栏设计。

```typescript
import DynamicIsland from '../components/device/DynamicIsland';

<DynamicIsland
  deviceTime="9:41"
  mode="light"
  className="custom-styles"
/>
```

#### 🔋 BatteryIcon - 电池图标组件
真实的 iPhone 电池图标，支持电量百分比和低电量警告。

```typescript
import BatteryIcon from '../components/icons/BatteryIcon';

<BatteryIcon
  percentage={85}
  charging={false}
  mode="light"
  color="currentColor"
  className="h-[11px]"
/>
```

**主要特性**：
- ✅ **电量显示**: 0-100% 电量百分比可视化
- ✅ **低电量警告**: ≤20% 时自动显示红色警告
- ✅ **充电状态**: 可选的充电图标显示
- ✅ **主题适配**: 支持明暗主题模式
- ✅ **真实渲染**: 基于真实 iPhone 电池图标 SVG

## 📘 TypeScript 接口文档

### 核心类型定义

#### Message - 消息接口
```typescript
interface Message {
  id: string;                    // 唯一标识符
  sender: 'user' | 'recipient' | 'system';  // 发送者类型
  content: string;               // 消息内容
  timestamp?: string;            // 时间戳（可选）
  status?: 'default' | 'delivered' | 'failed' | 'read';  // 消息状态
  imageUrl?: string;             // 图片URL（可选）
  method?: 'data' | 'image' | 'unknown';  // 消息类型
}
```

#### PhonePreviewProps - iPhone预览组件属性
```typescript
interface PhonePreviewProps {
  recipientName: string;         // 联系人姓名
  recipientAvatar: string | null; // 联系人头像
  messages: Message[];           // 消息列表
  deviceTime: string;            // 设备时间
  timeFormat: TimeFormat;        // 时间格式
  mode: ThemeMode;              // 主题模式
  batteryPercentage?: number;    // 电池电量百分比 (0-100)，默认85%
}
```

#### 基础类型
```typescript
type TimeFormat = '12' | '24';           // 时间格式：12小时制或24小时制
type ThemeMode = 'light' | 'dark';       // 主题模式：浅色或深色
type MessageSender = 'user' | 'recipient' | 'system';  // 消息发送者类型
```

### 工具函数 API

#### 时间处理函数
```typescript
// 格式化显示时间（状态栏专用，12小时制不显示AM/PM）
formatDisplayTime(time: string, format: TimeFormat): string

// 验证时间格式的有效性
validateTimeFormat(time: string, format: TimeFormat): {
  isValid: boolean;
  error: string;
}

// 格式化时间输入（带智能输入掩码）
formatTimeInput(value: string, format: TimeFormat): string

// 验证时间输入边界值（防止无效时间）
isValidTimeInput(hours: string, minutes: string, format: TimeFormat): boolean

// 通用时间格式化（支持AM/PM显示）
formatTime(time: string, targetFormat: TimeFormat): string
```

### 自定义 Hooks

#### useTimeInput - 时间输入管理
```typescript
const {
  deviceTime,              // 当前设备时间
  timeFormat,              // 时间格式
  timeError,               // 时间错误信息
  isTimeValid,             // 时间是否有效
  handleFormattedTimeChange, // 处理时间变更
  handleTimeFormatChange,   // 处理格式变更
  resetTime,               // 重置时间
  setDeviceTime,           // 设置设备时间
  setTimeFormat            // 设置时间格式
} = useTimeInput('9:41', '12');
```

#### useNotification - 通知系统
```typescript
const {
  toast,                   // Toast 通知方法
  modal,                   // 模态框方法
  utils                    // 工具方法
} = useNotification();

// 显示成功提示
toast.success('操作成功！');

// 显示确认对话框
const confirmed = await modal.confirm({
  title: '确认操作',
  message: '您确定要执行此操作吗？',
  confirmText: '确认',
  cancelText: '取消'
});
```

## 🚀 快速开始

### 环境要求

- Node.js 18+
- npm, yarn, pnpm 或 bun

### 安装步骤

1. **克隆仓库**

```bash
git clone https://github.com/yourusername/fake-text-message-generator.git
cd fake-text-message-generator
```

2. **安装依赖**

```bash
npm install
# 或者
yarn install
# 或者
pnpm install
```

3. **配置环境变量**

```bash
cp .env.example .env.local
```

4. **启动开发服务器**

```bash
npm run dev
# 或者
yarn dev
# 或者
pnpm dev
```

5. **打开浏览器**
   访问 [http://localhost:3000](http://localhost:3000)

## ⚙️ 环境变量配置

在项目根目录创建 `.env.local` 文件：

```env
# =============================================================================
# Google Analytics 4 配置
# =============================================================================
NEXT_PUBLIC_GA4_MEASUREMENT_ID=G-E5KYVM3REM

# =============================================================================
# Google Search Console 配置
# =============================================================================
NEXT_PUBLIC_GSC_VERIFICATION=your-verification-code

# =============================================================================
# 站点配置
# =============================================================================
NEXT_PUBLIC_SITE_URL=https://faketextmessage.xyz

# =============================================================================
# 隐私和合规性配置（可选）
# =============================================================================
NEXT_PUBLIC_ENABLE_COOKIE_CONSENT=false
NEXT_PUBLIC_PRIVACY_POLICY_URL=
NEXT_PUBLIC_TERMS_OF_SERVICE_URL=
```

### 环境变量说明

| 变量名                                | 说明                                    | 必需 |
| ------------------------------------- | --------------------------------------- | ---- |
| `NEXT_PUBLIC_GA4_MEASUREMENT_ID`    | Google Analytics 4 测量 ID              | 否   |
| `NEXT_PUBLIC_GSC_VERIFICATION`      | Google Search Console 验证代码          | 否   |
| `NEXT_PUBLIC_SITE_URL`              | 网站 URL 地址                           | 是   |
| `NEXT_PUBLIC_ENABLE_COOKIE_CONSENT` | 启用 Cookie 同意横幅                    | 否   |

## 📁 项目结构

```
fake-text-message-generator/
├── app/                          # Next.js App Router
│   ├── layout.tsx               # 根布局，包含 SEO 和分析
│   ├── page.tsx                 # 主应用页面
│   └── globals.css              # 全局样式
├── components/                   # React 组件
│   ├── device/                  # 设备相关组件
│   │   ├── PhonePreview.tsx     # iPhone 预览组件
│   │   └── DynamicIsland.tsx    # Dynamic Island 组件
│   ├── icons/                   # 图标组件
│   │   ├── SignalIcon.tsx       # 信号图标
│   │   ├── WiFiIcon.tsx         # WiFi 图标
│   │   └── BatteryIcon.tsx      # 电池图标（支持电量百分比和低电量警告）
│   ├── layout/                  # 布局组件
│   │   ├── Header.tsx           # 导航头部
│   │   └── Footer.tsx           # 站点底部
│   ├── sections/                # 页面区块
│   │   ├── FAQ.tsx              # 常见问题
│   │   └── HowToUse.tsx         # 使用说明
│   ├── seo/                     # SEO 组件
│   │   └── StructuredData.tsx   # Schema.org 结构化数据
│   └── ui/                      # UI 组件
│       ├── MessageBubble.tsx    # 消息气泡组件
│       ├── MessageInput.tsx     # 消息输入组件
│       └── notification/        # 通知系统
├── hooks/                        # 自定义 Hooks
│   ├── useTimeInput.ts          # 时间输入管理 Hook（完整时间状态管理）
│   └── useChangelog.ts          # 更新日志管理 Hook
├── types/                        # TypeScript 类型定义
│   ├── message.ts               # 消息相关类型（包含时间格式类型）
│   └── changelog.ts             # 更新日志类型定义
├── utils/                        # 工具函数
│   ├── download.ts              # 图片下载功能
│   ├── time.ts                  # 时间处理工具（格式化、验证、输入掩码）
│   └── changelog.ts             # 更新日志工具函数
├── public/                       # 静态资源
│   ├── robots.txt               # 搜索引擎指令
│   ├── sitemap.xml              # SEO 站点地图
│   ├── lmt.txt                  # AI 内容声明
│   └── *.svg                    # 图标资源
├── .env.example                 # 环境变量模板
├── .env.local                   # 本地环境变量（需创建）
├── next.config.ts               # Next.js 配置
├── tailwind.config.ts           # Tailwind CSS 配置
├── vercel.json                  # Vercel 部署配置
└── package.json                 # 依赖和脚本
```

## 🚀 Deployment

### Deploy to Vercel (Recommended)

1. **Prepare Your Code**

```bash
npm run build
npm run start
```

2. **Connect to Vercel**

   - Visit [vercel.com](https://vercel.com)
   - Import your project
   - Vercel will auto-detect Next.js
3. **Configure Environment Variables**
   In Vercel Dashboard → Settings → Environment Variables:

   ```
   NEXT_PUBLIC_GA4_MEASUREMENT_ID=G-XXXXXXXXXX
   NEXT_PUBLIC_GSC_VERIFICATION=your-verification-code
   NEXT_PUBLIC_SITE_URL=https://your-domain.com
   ```
4. **Custom Domain (Optional)**

   - Add your domain in Vercel Dashboard
   - Update DNS records as instructed
   - SSL certificate is automatically provided

### Alternative Deployment Options

- **Netlify**: Works with standard Next.js build
- **Railway**: Simple deployment with GitHub integration
- **DigitalOcean App Platform**: Scalable hosting option
- **Self-hosted**: Use `npm run build` and `npm start`

## 📊 SEO & Analytics

### Built-in SEO Features

- ✅ **Meta Tags** - Complete Open Graph and Twitter Cards
- ✅ **Structured Data** - Schema.org WebApplication markup
- ✅ **Sitemap** - XML sitemap for search engines
- ✅ **Robots.txt** - Search engine crawling directives
- ✅ **Canonical URLs** - Prevent duplicate content issues
- ✅ **Performance** - Optimized Core Web Vitals

### Analytics Integration

- **Google Analytics 4** - User behavior tracking
- **Google Search Console** - Search performance monitoring
- **Privacy Compliant** - GDPR-friendly configuration

### AI Content Declaration

This project includes an `lmt.txt` file declaring AI-assisted content in compliance with emerging AI transparency standards.

## 🎯 Usage Guide

### Creating Fake Text Messages

1. **Set Recipient Info**

   - Upload a custom avatar or use default
   - Enter recipient name
2. **Add Messages**

   - Type your message in the input field
   - Click "Add User Message" for messages from you
   - Click "Add Recipient Message" for replies
3. **Customize Appearance**

   - Toggle between light and dark mode
   - Adjust the time display
   - Preview updates in real-time
4. **Download**

   - Click "Download Image" button
   - High-quality PNG file will be saved
   - Filename includes timestamp for organization

### Best Practices

- **Keep it realistic** - Use natural conversation flow
- **Check spelling** - Messages can't be edited after download
- **Consider context** - Think about your intended use case
- **Respect privacy** - Don't create misleading content

## 🤝 Contributing

We welcome contributions! Here's how you can help:

### Development Setup

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes
4. Run tests: `npm run lint`
5. Commit changes: `git commit -m 'Add amazing feature'`
6. Push to branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

### Contribution Guidelines

- Follow the existing code style
- Add TypeScript types for new features
- Update documentation for significant changes
- Test your changes thoroughly
- Keep commits focused and descriptive

### Areas for Contribution

- 🎨 **UI/UX Improvements** - Better design and user experience
- 📱 **Device Support** - Android, WhatsApp, other messaging apps
- 🌍 **Internationalization** - Multi-language support
- ♿ **Accessibility** - Screen reader and keyboard navigation
- 🔧 **Features** - New customization options
- 🐛 **Bug Fixes** - Report and fix issues

## 📝 Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint

# Deployment
vercel               # Deploy to Vercel
vercel --prod        # Deploy to production
```

## 🔧 Configuration

### Tailwind CSS

The project uses Tailwind CSS 4 with custom configuration:

- Apple-style design system
- Custom color palette
- Responsive breakpoints
- Dark mode support

### Next.js

Optimized Next.js configuration includes:

- Image optimization
- Font optimization (Geist)
- Security headers
- SEO enhancements

## 🐛 Troubleshooting

### Common Issues

**Port already in use**

```bash
# Kill process on port 3000
npx kill-port 3000
# Or use different port
npm run dev -- -p 3001
```

**Environment variables not loading**

- Ensure `.env.local` exists in root directory
- Restart development server after changes
- Check variable names have `NEXT_PUBLIC_` prefix for client-side access

**Image download not working**

- Check browser permissions for downloads
- Ensure html2canvas dependency is installed
- Try different browser if issues persist

**Build errors**

```bash
# Clear Next.js cache
rm -rf .next
npm run build
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Next.js Team** - Amazing React framework
- **Vercel** - Excellent hosting platform
- **Tailwind CSS** - Utility-first CSS framework
- **html2canvas** - Client-side screenshot library
- **Apple** - Design inspiration from iOS

## 📞 Support

- 🌐 **Website**: [https://faketextmessage.xyz](https://faketextmessage.xyz)
- 📧 **Contact**: [Contact Us](https://faketextmessage.xyz/contact)
- 💬 **Help**: [Help Center](https://faketextmessage.xyz/help)

---

<div align="center">

**Made with ❤️ for creators and developers**

[🌐 Visit Website](https://faketextmessage.xyz) • [📧 Contact Us](https://faketextmessage.xyz/contact) • [❓ Get Help](https://faketextmessage.xyz/help)

</div>
