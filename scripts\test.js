#!/usr/bin/env node

/**
 * 简单的测试运行脚本
 * 适合个人项目的轻量级测试解决方案
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class SimpleTestRunner {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.results = {
      build: null,
      lint: null,
      typeCheck: null,
      functional: null
    };
  }

  /**
   * 输出带颜色的日志
   */
  log(message, type = 'info') {
    const colors = {
      info: '\x1b[36m',    // 青色
      success: '\x1b[32m', // 绿色
      warning: '\x1b[33m', // 黄色
      error: '\x1b[31m',   // 红色
      reset: '\x1b[0m'     // 重置
    };
    
    const color = colors[type] || colors.info;
    console.log(`${color}${message}${colors.reset}`);
  }

  /**
   * 运行命令并捕获结果
   */
  runCommand(command, description) {
    this.log(`\n🔄 ${description}...`, 'info');
    
    try {
      const output = execSync(command, {
        cwd: this.projectRoot,
        encoding: 'utf8',
        stdio: 'pipe'
      });
      
      this.log(`✅ ${description} 成功`, 'success');
      return { success: true, output };
    } catch (error) {
      this.log(`❌ ${description} 失败`, 'error');
      this.log(error.message, 'error');
      return { success: false, error: error.message };
    }
  }

  /**
   * 检查构建
   */
  testBuild() {
    this.log('\n📦 测试构建...', 'info');
    this.results.build = this.runCommand('npm run build', '项目构建');
    return this.results.build.success;
  }

  /**
   * 检查代码规范
   */
  testLint() {
    this.log('\n🔍 检查代码规范...', 'info');
    
    // 检查是否有lint脚本
    const packageJson = JSON.parse(fs.readFileSync(path.join(this.projectRoot, 'package.json'), 'utf8'));
    
    if (packageJson.scripts && packageJson.scripts.lint) {
      this.results.lint = this.runCommand('npm run lint', '代码规范检查');
    } else {
      this.log('⚠️ 未找到lint脚本，跳过代码规范检查', 'warning');
      this.results.lint = { success: true, skipped: true };
    }
    
    return this.results.lint.success;
  }

  /**
   * 检查TypeScript类型
   */
  testTypeCheck() {
    this.log('\n🔧 检查TypeScript类型...', 'info');
    
    // 检查是否有TypeScript配置
    const tsconfigExists = fs.existsSync(path.join(this.projectRoot, 'tsconfig.json'));
    
    if (tsconfigExists) {
      this.results.typeCheck = this.runCommand('npx tsc --noEmit', 'TypeScript类型检查');
    } else {
      this.log('⚠️ 未找到tsconfig.json，跳过类型检查', 'warning');
      this.results.typeCheck = { success: true, skipped: true };
    }
    
    return this.results.typeCheck.success;
  }

  /**
   * 检查关键文件
   */
  testFileStructure() {
    this.log('\n📁 检查文件结构...', 'info');
    
    const criticalFiles = [
      'package.json',
      'next.config.ts',
      'tailwind.config.ts',
      'app/page.tsx',
      'app/layout.tsx'
    ];
    
    const missingFiles = [];
    
    criticalFiles.forEach(file => {
      const filePath = path.join(this.projectRoot, file);
      if (!fs.existsSync(filePath)) {
        missingFiles.push(file);
      }
    });
    
    if (missingFiles.length === 0) {
      this.log('✅ 所有关键文件存在', 'success');
      return true;
    } else {
      this.log(`❌ 缺少关键文件: ${missingFiles.join(', ')}`, 'error');
      return false;
    }
  }

  /**
   * 检查依赖
   */
  testDependencies() {
    this.log('\n📦 检查依赖...', 'info');
    
    try {
      const packageJson = JSON.parse(fs.readFileSync(path.join(this.projectRoot, 'package.json'), 'utf8'));
      const nodeModulesExists = fs.existsSync(path.join(this.projectRoot, 'node_modules'));
      
      if (!nodeModulesExists) {
        this.log('❌ node_modules 不存在，请运行 npm install', 'error');
        return false;
      }
      
      // 检查关键依赖
      const criticalDeps = ['next', 'react', 'tailwindcss'];
      const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };
      
      const missingDeps = criticalDeps.filter(dep => !allDeps[dep]);
      
      if (missingDeps.length === 0) {
        this.log('✅ 所有关键依赖存在', 'success');
        return true;
      } else {
        this.log(`❌ 缺少关键依赖: ${missingDeps.join(', ')}`, 'error');
        return false;
      }
    } catch (error) {
      this.log(`❌ 检查依赖失败: ${error.message}`, 'error');
      return false;
    }
  }

  /**
   * 生成功能验证说明
   */
  generateFunctionalTestInstructions() {
    this.log('\n🧪 功能验证说明', 'info');
    
    const instructions = `
📋 手动功能验证步骤:

1. 启动开发服务器:
   npm run dev

2. 在浏览器中打开: http://localhost:3000

3. 在浏览器控制台中运行功能验证脚本:
   - 复制 tests/functional-validation.js 的内容
   - 粘贴到浏览器控制台并运行

4. 使用手动测试清单:
   - 参考 tests/manual-test-checklist.md
   - 逐项验证功能

5. 测试不同浏览器和设备:
   - Chrome, Firefox, Safari, Edge
   - 桌面端和移动端

💡 提示:
- 功能验证脚本会自动检查基础功能
- 手动测试清单覆盖用户体验测试
- 建议在每次重要更新后运行完整测试
`;
    
    console.log(instructions);
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    this.log('\n📊 测试报告', 'info');
    this.log('='.repeat(50), 'info');
    
    const tests = [
      { name: '文件结构', result: this.testFileStructure() },
      { name: '依赖检查', result: this.testDependencies() },
      { name: '代码规范', result: this.results.lint?.success },
      { name: 'TypeScript', result: this.results.typeCheck?.success },
      { name: '项目构建', result: this.results.build?.success }
    ];
    
    let passed = 0;
    let total = 0;
    
    tests.forEach(test => {
      if (test.result !== undefined) {
        total++;
        if (test.result) {
          passed++;
          this.log(`✅ ${test.name}`, 'success');
        } else {
          this.log(`❌ ${test.name}`, 'error');
        }
      } else {
        this.log(`⚠️ ${test.name} (跳过)`, 'warning');
      }
    });
    
    this.log(`\n📈 总体结果: ${passed}/${total} 通过`, passed === total ? 'success' : 'warning');
    
    if (passed === total) {
      this.log('\n🎉 所有自动化测试通过！', 'success');
      this.log('建议继续进行功能验证测试', 'info');
    } else {
      this.log('\n⚠️ 部分测试失败，请检查并修复', 'warning');
    }
    
    return { passed, total, success: passed === total };
  }

  /**
   * 运行所有测试
   */
  async runAll() {
    this.log('🚀 开始运行测试套件...', 'info');
    this.log(`项目路径: ${this.projectRoot}`, 'info');
    
    // 运行基础检查
    const structureOk = this.testFileStructure();
    const depsOk = this.testDependencies();
    
    if (!structureOk || !depsOk) {
      this.log('\n❌ 基础检查失败，跳过其他测试', 'error');
      return false;
    }
    
    // 运行其他测试
    this.testLint();
    this.testTypeCheck();
    this.testBuild();
    
    // 生成报告
    const report = this.generateReport();
    
    // 生成功能验证说明
    this.generateFunctionalTestInstructions();
    
    return report.success;
  }
}

// 运行测试
if (require.main === module) {
  const runner = new SimpleTestRunner();
  runner.runAll().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = SimpleTestRunner;
