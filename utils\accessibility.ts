/**
 * 无障碍功能工具
 * 提供键盘导航、焦点管理、屏幕阅读器支持等功能
 */

/**
 * 键盘导航管理器
 */
export class KeyboardNavigationManager {
  private focusableElements: HTMLElement[] = [];
  private currentIndex = -1;
  private container: HTMLElement | null = null;

  constructor(containerSelector?: string) {
    if (typeof window !== 'undefined') {
      if (containerSelector) {
        this.container = document.querySelector(containerSelector);
      } else {
        this.container = document.body;
      }
      this.updateFocusableElements();
    }
  }

  /**
   * 更新可聚焦元素列表
   */
  updateFocusableElements(): void {
    if (!this.container) return;

    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[role="button"]:not([disabled])',
      '[role="link"]',
      '[role="menuitem"]'
    ].join(', ');

    this.focusableElements = Array.from(
      this.container.querySelectorAll(focusableSelectors)
    ).filter(el => this.isVisible(el as HTMLElement)) as HTMLElement[];
  }

  /**
   * 检查元素是否可见
   */
  private isVisible(element: HTMLElement): boolean {
    const style = window.getComputedStyle(element);
    return style.display !== 'none' && 
           style.visibility !== 'hidden' && 
           style.opacity !== '0' &&
           element.offsetWidth > 0 && 
           element.offsetHeight > 0;
  }

  /**
   * 聚焦到下一个元素
   */
  focusNext(): void {
    this.updateFocusableElements();
    if (this.focusableElements.length === 0) return;

    this.currentIndex = (this.currentIndex + 1) % this.focusableElements.length;
    this.focusableElements[this.currentIndex].focus();
  }

  /**
   * 聚焦到上一个元素
   */
  focusPrevious(): void {
    this.updateFocusableElements();
    if (this.focusableElements.length === 0) return;

    this.currentIndex = this.currentIndex <= 0 
      ? this.focusableElements.length - 1 
      : this.currentIndex - 1;
    this.focusableElements[this.currentIndex].focus();
  }

  /**
   * 聚焦到第一个元素
   */
  focusFirst(): void {
    this.updateFocusableElements();
    if (this.focusableElements.length === 0) return;

    this.currentIndex = 0;
    this.focusableElements[0].focus();
  }

  /**
   * 聚焦到最后一个元素
   */
  focusLast(): void {
    this.updateFocusableElements();
    if (this.focusableElements.length === 0) return;

    this.currentIndex = this.focusableElements.length - 1;
    this.focusableElements[this.currentIndex].focus();
  }
}

/**
 * 焦点陷阱管理器
 * 用于模态框等需要限制焦点范围的组件
 */
export class FocusTrap {
  private container: HTMLElement;
  private previousFocus: HTMLElement | null = null;
  private keyboardManager: KeyboardNavigationManager;

  constructor(container: HTMLElement) {
    this.container = container;
    this.keyboardManager = new KeyboardNavigationManager();
    this.keyboardManager['container'] = container;
  }

  /**
   * 激活焦点陷阱
   */
  activate(): void {
    if (typeof document === 'undefined') return;

    this.previousFocus = document.activeElement as HTMLElement;
    this.keyboardManager.focusFirst();
    document.addEventListener('keydown', this.handleKeyDown);
  }

  /**
   * 停用焦点陷阱
   */
  deactivate(): void {
    if (typeof document === 'undefined') return;

    document.removeEventListener('keydown', this.handleKeyDown);
    if (this.previousFocus) {
      this.previousFocus.focus();
    }
  }

  /**
   * 处理键盘事件
   */
  private handleKeyDown = (event: KeyboardEvent): void => {
    if (event.key === 'Tab') {
      event.preventDefault();
      if (event.shiftKey) {
        this.keyboardManager.focusPrevious();
      } else {
        this.keyboardManager.focusNext();
      }
    } else if (event.key === 'Escape') {
      this.deactivate();
    }
  };
}

/**
 * 屏幕阅读器公告管理器
 */
export class ScreenReaderAnnouncer {
  private liveRegion: HTMLElement | null = null;

  constructor() {
    // 延迟初始化，确保在客户端环境下才创建
    if (typeof document !== 'undefined') {
      this.liveRegion = this.createLiveRegion();
    }
  }

  /**
   * 创建实时区域元素
   */
  private createLiveRegion(): HTMLElement | null {
    // 确保在客户端环境下才执行
    if (typeof document === 'undefined') return null;

    const existing = document.getElementById('sr-live-region');
    if (existing) return existing;

    const liveRegion = document.createElement('div');
    liveRegion.id = 'sr-live-region';
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.style.position = 'absolute';
    liveRegion.style.left = '-10000px';
    liveRegion.style.width = '1px';
    liveRegion.style.height = '1px';
    liveRegion.style.overflow = 'hidden';

    document.body.appendChild(liveRegion);
    return liveRegion;
  }

  /**
   * 发布公告
   */
  announce(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
    // 如果在服务端渲染或liveRegion未创建，则跳过
    if (!this.liveRegion) {
      // 尝试重新创建（可能是在客户端首次调用）
      if (typeof document !== 'undefined') {
        this.liveRegion = this.createLiveRegion();
      }
      if (!this.liveRegion) return;
    }

    this.liveRegion.setAttribute('aria-live', priority);
    this.liveRegion.textContent = message;

    // 清除消息以便下次相同消息也能被读出
    setTimeout(() => {
      if (this.liveRegion) {
        this.liveRegion.textContent = '';
      }
    }, 1000);
  }
}

/**
 * 颜色对比度检查器
 */
export class ColorContrastChecker {
  /**
   * 计算相对亮度
   */
  private getRelativeLuminance(r: number, g: number, b: number): number {
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  }

  /**
   * 计算对比度比率
   */
  getContrastRatio(color1: string, color2: string): number {
    const rgb1 = this.hexToRgb(color1);
    const rgb2 = this.hexToRgb(color2);

    if (!rgb1 || !rgb2) return 0;

    const l1 = this.getRelativeLuminance(rgb1.r, rgb1.g, rgb1.b);
    const l2 = this.getRelativeLuminance(rgb2.r, rgb2.g, rgb2.b);

    const lighter = Math.max(l1, l2);
    const darker = Math.min(l1, l2);

    return (lighter + 0.05) / (darker + 0.05);
  }

  /**
   * 将十六进制颜色转换为RGB
   */
  private hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }

  /**
   * 检查是否符合WCAG标准
   */
  checkWCAGCompliance(foreground: string, background: string): {
    ratio: number;
    aa: boolean;
    aaa: boolean;
  } {
    const ratio = this.getContrastRatio(foreground, background);
    return {
      ratio,
      aa: ratio >= 4.5,
      aaa: ratio >= 7
    };
  }
}

/**
 * 全局无障碍管理器
 */
export class AccessibilityManager {
  private keyboardManager: KeyboardNavigationManager;
  private announcer: ScreenReaderAnnouncer;
  private contrastChecker: ColorContrastChecker;

  constructor() {
    this.keyboardManager = new KeyboardNavigationManager();
    this.announcer = new ScreenReaderAnnouncer();
    this.contrastChecker = new ColorContrastChecker();

    // 只在客户端环境下初始化
    if (typeof document !== 'undefined') {
      this.init();
    }
  }

  /**
   * 初始化无障碍功能
   */
  private init(): void {
    this.setupKeyboardNavigation();
    this.setupSkipLinks();
    this.setupFocusIndicators();
  }

  /**
   * 设置键盘导航
   */
  private setupKeyboardNavigation(): void {
    if (typeof document === 'undefined') return;

    document.addEventListener('keydown', (event) => {
      // Alt + 1: 跳转到主内容
      if (event.altKey && event.key === '1') {
        event.preventDefault();
        const main = document.querySelector('main');
        if (main) {
          main.focus();
          this.announcer.announce('跳转到主内容');
        }
      }

      // Alt + 2: 跳转到导航
      if (event.altKey && event.key === '2') {
        event.preventDefault();
        const nav = document.querySelector('nav');
        if (nav) {
          nav.focus();
          this.announcer.announce('跳转到导航');
        }
      }
    });
  }

  /**
   * 设置跳转链接
   */
  private setupSkipLinks(): void {
    if (typeof document === 'undefined') return;

    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.textContent = 'Skip to main content';
    skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 bg-blue-600 text-white p-2 z-50';
    skipLink.addEventListener('click', (e) => {
      e.preventDefault();
      const main = document.querySelector('#main-content, main');
      if (main) {
        (main as HTMLElement).focus();
      }
    });

    document.body.insertBefore(skipLink, document.body.firstChild);
  }

  /**
   * 设置焦点指示器
   */
  private setupFocusIndicators(): void {
    if (typeof document === 'undefined') return;

    const style = document.createElement('style');
    style.textContent = `
      .focus-visible {
        outline: 2px solid #3b82f6;
        outline-offset: 2px;
      }

      .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 公告消息
   */
  announce(message: string, priority?: 'polite' | 'assertive'): void {
    this.announcer.announce(message, priority);
  }

  /**
   * 检查颜色对比度
   */
  checkContrast(foreground: string, background: string) {
    return this.contrastChecker.checkWCAGCompliance(foreground, background);
  }

  /**
   * 创建焦点陷阱
   */
  createFocusTrap(container: HTMLElement): FocusTrap {
    return new FocusTrap(container);
  }
}

// 创建全局实例
export const accessibilityManager = new AccessibilityManager();
