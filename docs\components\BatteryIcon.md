# BatteryIcon 组件文档

## 概述

`BatteryIcon` 是一个高度可配置的电池图标组件，提供真实的 iPhone 电池外观和智能电量显示功能。该组件支持电量百分比可视化、低电量警告、充电状态显示和主题适配。

## 功能特性

- ✅ **真实外观** - 基于真实 iPhone 电池图标 SVG 设计
- ✅ **电量可视化** - 0-100% 电量百分比动态填充显示
- ✅ **低电量警告** - ≤20% 时自动显示红色警告色
- ✅ **充电状态** - 可选的充电图标叠加显示
- ✅ **主题适配** - 支持明暗主题模式
- ✅ **平滑动画** - 电量变化时的过渡动画效果

## 组件接口

```typescript
interface BatteryIconProps {
  percentage?: number;    // 电量百分比 (0-100)，默认85
  charging?: boolean;     // 是否显示充电状态，默认false
  className?: string;     // 自定义CSS类名
  color?: string;         // 图标颜色，默认'currentColor'
  mode?: 'light' | 'dark'; // 主题模式，默认'light'
}
```

## 使用示例

### 基础使用

```typescript
import BatteryIcon from '../components/icons/BatteryIcon';

// 默认电量85%
<BatteryIcon />

// 自定义电量
<BatteryIcon percentage={65} />
```

### 低电量警告

```typescript
// 低电量状态（≤20%时自动显示红色）
<BatteryIcon percentage={15} />

// 极低电量
<BatteryIcon percentage={5} />
```

### 充电状态

```typescript
// 显示充电图标
<BatteryIcon 
  percentage={45} 
  charging={true} 
/>

// 充电中的低电量
<BatteryIcon 
  percentage={18} 
  charging={true} 
/>
```

### 主题适配

```typescript
// 浅色主题
<BatteryIcon 
  percentage={75} 
  mode="light" 
  color="currentColor"
/>

// 深色主题
<BatteryIcon 
  percentage={75} 
  mode="dark" 
  color="white"
/>
```

### 自定义样式

```typescript
// 自定义尺寸和样式
<BatteryIcon 
  percentage={90} 
  className="h-[11px] w-auto"
  color="#007AFF"
/>

// 在状态栏中使用
<BatteryIcon 
  percentage={batteryPercentage}
  mode={themeMode}
  color="currentColor"
  className="h-[11px]"
/>
```

## 电量状态逻辑

### 电量颜色规则

```typescript
// 电量颜色逻辑
const fillColor = percentage > 20 ? color : '#FF3B30';

// 正常电量：使用传入的color属性
// 低电量（≤20%）：强制使用红色 #FF3B30
```

### 电量填充计算

```typescript
// 电量填充宽度计算
const fillWidth = Math.max(0, Math.min(100, percentage));
const fillPixelWidth = (fillWidth / 100) * 15.5; // 根据SVG内部空间调整
```

### 充电图标显示

```typescript
// 充电图标条件显示
{charging && (
  <div className="absolute inset-0 flex items-center justify-center">
    <svg className="w-2 h-2" viewBox="0 0 8 8" fill={mode === 'dark' ? '#000' : '#FFF'}>
      <path d="M4.5 0L2 3h1.5v2L6 2H4.5V0z"/>
    </svg>
  </div>
)}
```

## 技术实现

### SVG 结构

```typescript
// 电池主体SVG（基于真实iPhone设计）
<svg viewBox="0 0 29 13" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M4 2C1.79086 2 0 3.79086 0 6V7C0 9.20914 1.79086 11 4 11H21C23.2091 11 25 9.20914 25 7V6C25 3.79086 23.2091 2 21 2H4Z" fill="none" stroke="currentColor" strokeWidth="1"/>
  <path d="M26 4.5C26 4.22386 26.2239 4 26.5 4H27.5C27.7761 4 28 4.22386 28 4.5V8.5C28 8.77614 27.7761 9 27.5 9H26.5C26.2239 9 26 8.77614 26 8.5V4.5Z" fill="currentColor"/>
</svg>
```

### 电量填充实现

```typescript
// 动态电量填充条
<div 
  className="absolute top-[2.5px] left-[2.2px] h-[6px] transition-all duration-300 rounded-[1px]"
  style={{ 
    backgroundColor: fillColor,
    width: `${(fillWidth / 100) * 15.5}px`,
  }}
></div>
```

### 动画效果

```typescript
// CSS过渡动画
.transition-all.duration-300 {
  transition-property: all;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
```

## 在项目中的使用

### PhonePreview 组件中

```typescript
// 在iPhone状态栏中使用
<BatteryIcon
  percentage={batteryPercentage}
  mode={mode}
  color="currentColor"
  className="h-[11px]"
/>
```

### 状态栏集成

```typescript
// 与其他状态栏图标一起使用
<div className="flex items-center gap-[5px]">
  <SignalIcon strength={4} color="currentColor" />
  <WiFiIcon connected={true} color="currentColor" />
  <BatteryIcon 
    percentage={batteryPercentage} 
    mode={mode} 
    color="currentColor" 
    className="h-[11px]" 
  />
</div>
```

## 最佳实践

### 1. 电量范围验证

```typescript
// 确保电量值在有效范围内
const validPercentage = Math.max(0, Math.min(100, inputPercentage));
<BatteryIcon percentage={validPercentage} />
```

### 2. 主题一致性

```typescript
// 保持与应用主题一致
<BatteryIcon 
  percentage={battery} 
  mode={appTheme} 
  color={appTheme === 'dark' ? 'white' : 'black'}
/>
```

### 3. 响应式设计

```typescript
// 在不同屏幕尺寸下保持合适的大小
<BatteryIcon 
  percentage={battery} 
  className="h-[11px] sm:h-[12px] md:h-[13px]"
/>
```

### 4. 无障碍支持

```typescript
// 添加无障碍属性
<div aria-label={`Battery level: ${percentage}%`}>
  <BatteryIcon percentage={percentage} />
</div>
```

## 性能优化

### 1. 避免频繁重渲染

```typescript
// 使用 useMemo 优化电量计算
const batteryProps = useMemo(() => ({
  percentage: batteryLevel,
  charging: isCharging,
  mode: themeMode
}), [batteryLevel, isCharging, themeMode]);

<BatteryIcon {...batteryProps} />
```

### 2. 条件渲染优化

```typescript
// 仅在电量变化时重新渲染
const MemoizedBatteryIcon = React.memo(BatteryIcon);
<MemoizedBatteryIcon percentage={battery} />
```

## 故障排除

### 常见问题

1. **电池图标不显示**
   - 检查 `className` 是否包含正确的高度设置
   - 确认 `color` 属性不是透明色

2. **电量填充不正确**
   - 验证 `percentage` 值在 0-100 范围内
   - 检查 SVG viewBox 和填充计算是否匹配

3. **充电图标显示异常**
   - 确认 `charging` 属性为 boolean 类型
   - 检查充电图标的 `fill` 颜色设置

4. **主题切换问题**
   - 确保 `mode` 属性正确传递
   - 检查充电图标的颜色逻辑

## 扩展性

### 添加新的电池状态

```typescript
// 扩展电池状态类型
type BatteryStatus = 'normal' | 'low' | 'critical' | 'charging';

// 根据状态显示不同样式
const getBatteryColor = (percentage: number, status: BatteryStatus) => {
  switch (status) {
    case 'critical': return '#FF3B30';
    case 'low': return '#FF9500';
    case 'charging': return '#34C759';
    default: return 'currentColor';
  }
};
```

### 自定义电池形状

```typescript
// 支持不同的电池图标样式
interface BatteryIconProps {
  variant?: 'iphone' | 'android' | 'custom';
  // ... 其他属性
}
```

## 总结

BatteryIcon 组件为假短信生成器提供了真实的电池状态显示功能，具有以下优势：

- ✅ 高度还原 iPhone 电池图标外观
- ✅ 智能的电量状态管理和视觉反馈
- ✅ 完整的主题适配和响应式支持
- ✅ 优秀的性能和可扩展性
- ✅ 丰富的配置选项和使用场景

该组件确保了电池状态显示的真实性和用户体验的一致性，为项目的整体质量提供了重要保障。
