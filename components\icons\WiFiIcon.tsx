import React from 'react';

interface WiFiIconProps {
  connected?: boolean;
  className?: string;
  color?: string;
}

const WiFiIcon: React.FC<WiFiIconProps> = ({
  connected = true,
  className = '',
  color = 'currentColor'
}) => {
  return (
    <svg 
      className={`w-4 h-3 ${className}`} 
      viewBox="0 0 16 12" 
      fill={connected ? color : 'none'}
      stroke={connected ? 'none' : color}
      strokeWidth={connected ? 0 : 1}
    >
      {/* 外层弧线 */}
      <path d="M8 0C3.6 0 0 2.7 0 6c0 .3.2.5.5.5s.5-.2.5-.5c0-2.7 2.4-5 5-5s5 2.3 5 5c0 .3.2.5.5.5s.5-.2.5-.5c0-3.3-3.6-6-8-6z"/>
      
      {/* 中层弧线 */}
      <path d="M8 3c-1.7 0-3 1.3-3 3 0 .3.2.5.5.5s.5-.2.5-.5c0-1.1.9-2 2-2s2 .9 2 2c0 .3.2.5.5.5s.5-.2.5-.5c0-1.7-1.3-3-3-3z"/>
      
      {/* 中心点 */}
      <circle cx="8" cy="9" r="1"/>
    </svg>
  );
};

export default WiFiIcon;
