/* iOS 13 Message Bubble Styles - Adapted for React Project */

.messageContainer {
  font-family: "Helvetica Neue", Helvetica, -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 13px;
  font-weight: normal;
  display: flex;
  flex-direction: column;
}

.messageBubble {
  max-width: 198px;
  word-wrap: break-word;
  margin-bottom: 6px;
  line-height: 18px;
  position: relative;
  padding: 6px 10px;
  border-radius: 14px;
  overflow: visible;
}

.messageBubble::before,
.messageBubble::after {
  content: "";
  position: absolute;
  bottom: 0;
  height: 18px;
}

/* Send message styles (user messages) */
.send {
  color: var(--send-color);
  background: var(--send-bg);
  align-self: flex-end;
}

.send::before {
  right: -5px;
  width: 15px;
  background-color: var(--send-bg);
  border-bottom-left-radius: 12px 10px;
}

.send::after {
  right: -20px;
  width: 20px;
  background-color: var(--tail-bg-color);
  border-bottom-left-radius: 8px;
}

/* Receive message styles (recipient messages) */
.receive {
  background: var(--receive-bg);
  color: var(--receive-text);
  align-self: flex-start;
}

.receive::before {
  left: -5px;
  width: 15px;
  background-color: var(--receive-bg);
  border-bottom-right-radius: 12px 10px;
}

.receive::after {
  left: -20px;
  width: 20px;
  background-color: var(--tail-bg-color);
  border-bottom-right-radius: 8px;
}

/* System message styles */
.system {
  background: var(--system-bg);
  color: var(--system-color);
  align-self: flex-end;
}

.system::before {
  right: -5px;
  width: 15px;
  background-color: var(--system-bg);
  border-bottom-left-radius: 12px 10px;
}

.system::after {
  right: -20px;
  width: 20px;
  background-color: var(--tail-bg-color);
  border-bottom-left-radius: 8px;
}

/* Message text content */
.messageText {
  width: 100%;
  white-space: pre-wrap;
  font-size: 13px;
  line-height: 16px;
}

/* Container for message alignment */
.messageWrapper {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.messageWrapper.alignEnd {
  align-items: flex-end;
}

.messageWrapper.alignStart {
  align-items: flex-start;
}
