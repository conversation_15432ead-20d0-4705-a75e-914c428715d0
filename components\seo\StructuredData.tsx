import Script from 'next/script';

export default function StructuredData() {
  const webApplicationSchema = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Fake Text Message Generator",
    "description": "Create realistic fake text messages and iPhone Messages conversations with our free text message generator. Features advanced time management system, smart battery control, real-time WYSIWYG preview, image upload, and intelligent role-based messaging. Perfect for creative projects, videos, and mockups.",
    "url": process.env.NEXT_PUBLIC_SITE_URL || "https://faketextmessage.xyz",
    "applicationCategory": "UtilityApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "iPhone 17/18 Messages interface design",
      "Real-time WYSIWYG preview",
      "Image upload and mixed content support",
      "Smart role system with color indicators",
      "Advanced time management with intelligent input masking",
      "12/24-hour format switching with real-time validation",
      "Editable date picker with hover trigger",
      "Smart battery management with configurable percentage (0-100%)",
      "Low battery warning display with authentic red color indicator",
      "Real-time battery level synchronization across interface",
      "Message status indicators (delivered/failed/read)",
      "CSS pseudo-element message tails",
      "Light and dark theme support",
      "Professional PNG export",
      "Mobile responsive design"
    ],

    "author": {
      "@type": "Organization",
      "name": "Fake Text Generator"
    }
  };

  const howToSchema = {
    "@context": "https://schema.org",
    "@type": "HowTo",
    "name": "How to Create Fake Text Messages",
    "description": "Step-by-step guide on how to create realistic fake text messages using our free text message generator",
    "image": `${process.env.NEXT_PUBLIC_SITE_URL || "https://faketextmessage.xyz"}/og-image.svg`,
    "totalTime": "PT5M",
    "estimatedCost": {
      "@type": "MonetaryAmount",
      "currency": "USD",
      "value": "0"
    },
    "step": [
      {
        "@type": "HowToStep",
        "name": "Set Recipient Information",
        "text": "Enter the recipient's name and optionally upload an avatar image",
        "image": `${process.env.NEXT_PUBLIC_SITE_URL || "https://faketextmessage.xyz"}/og-image.jpg`
      },
      {
        "@type": "HowToStep", 
        "name": "Add Messages",
        "text": "Type your messages and choose whether they're from the user or recipient",
        "image": `${process.env.NEXT_PUBLIC_SITE_URL || "https://faketextmessage.xyz"}/og-image.jpg`
      },
      {
        "@type": "HowToStep",
        "name": "Customize Settings",
        "text": "Use the advanced time management system to set device time with intelligent input masking, configure battery percentage (0-100%) with low battery warnings, switch between 12/24-hour formats, choose light or dark mode, and customize message timestamps with the editable date picker",
        "image": `${process.env.NEXT_PUBLIC_SITE_URL || "https://faketextmessage.xyz"}/og-image.jpg`
      },
      {
        "@type": "HowToStep",
        "name": "Download Image",
        "text": "Click the download button to save your fake text conversation as an image",
        "image": `${process.env.NEXT_PUBLIC_SITE_URL || "https://faketextmessage.xyz"}/og-image.jpg`
      }
    ]
  };

  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "How to generate fake texts?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Creating fake text messages is easy with our Fake Text Generator. Start by customizing recipient avatar, recipient name, and messages. Once you are happy with the result, you can download the fake text messages as an image and use it in your videos."
        }
      },
      {
        "@type": "Question",
        "name": "Is the Fake Text Generator free to use?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes, the Fake Text Generator is completely free to use. We believe in offering valuable tools to enhance your creative process at no cost."
        }
      },
      {
        "@type": "Question",
        "name": "Is it okay to use fake texts?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Using fake text texts for parody, education, or mockups is generally acceptable. However, you should always make it clear that the fake texts are not real to avoid misinformation."
        }
      },
      {
        "@type": "Question",
        "name": "Why aren't all my messages visible in the downloaded image?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "If you have multiple messages that extend beyond the visible area, you can scroll to position the messages exactly where you want them before downloading. The screenshot will capture the messages exactly as they appear in the preview."
        }
      }
    ]
  };

  // 网站结构化数据
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Fake Text Message Generator",
    "url": process.env.NEXT_PUBLIC_SITE_URL || "https://faketextmessage.xyz",
    "description": "Create realistic fake text messages and SMS conversations for free with our fake text generator. Perfect for text story videos, mockups, and creative projects.",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${process.env.NEXT_PUBLIC_SITE_URL || "https://faketextmessage.xyz"}/#generator`
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Fake Text Generator"
    }
  };

  // 面包屑导航结构化数据
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": process.env.NEXT_PUBLIC_SITE_URL || "https://faketextmessage.xyz"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Generator",
        "item": `${process.env.NEXT_PUBLIC_SITE_URL || "https://faketextmessage.xyz"}/#generator`
      }
    ]
  };

  return (
    <>
      <Script
        id="web-application-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(webApplicationSchema),
        }}
      />
      <Script
        id="how-to-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(howToSchema),
        }}
      />
      <Script
        id="faq-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(faqSchema),
        }}
      />
      <Script
        id="website-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema),
        }}
      />
      <Script
        id="breadcrumb-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbSchema),
        }}
      />
    </>
  );
}
