# PhonePreview 组件文档

## 概述

`PhonePreview` 是项目的核心组件，提供完整的 iPhone 界面模拟，包括 Dynamic Island、状态栏、聊天界面和输入区域。

## 功能特性

- ✅ **完整 iPhone 界面** - 高度还原 iPhone 15 系列设计
- ✅ **Dynamic Island** - 现代化的状态栏设计
- ✅ **响应式设计** - 适配不同屏幕尺寸
- ✅ **主题切换** - 支持浅色和深色模式
- ✅ **消息渲染** - 集成 MessageBubble 组件
- ✅ **截图支持** - 优化的 html2canvas 兼容性

## 组件接口

```typescript
interface PhonePreviewProps {
  recipientName: string;         // 联系人姓名
  recipientAvatar: string | null; // 联系人头像URL
  messages: Message[];           // 消息列表
  deviceTime: string;            // 设备时间显示
  timeFormat: TimeFormat;        // 时间格式（12/24小时制）
  mode: ThemeMode;              // 主题模式（light/dark）
}
```

## 使用示例

### 基础使用

```typescript
import PhonePreview from '../components/device/PhonePreview';
import { Message } from '../types/message';

const messages: Message[] = [
  {
    id: '1',
    sender: 'user',
    content: '你好！',
    timestamp: '10:30',
    status: 'delivered'
  },
  {
    id: '2',
    sender: 'recipient',
    content: '嗨，最近怎么样？',
    timestamp: '10:31',
    status: 'read'
  }
];

<PhonePreview
  recipientName="张三"
  recipientAvatar="/avatar.jpg"
  messages={messages}
  deviceTime="9:41"
  timeFormat="12"
  mode="light"
/>
```

### 空消息状态

```typescript
// 没有消息时显示提示
<PhonePreview
  recipientName="新联系人"
  recipientAvatar={null}
  messages={[]}
  deviceTime="9:41"
  timeFormat="24"
  mode="light"
/>
```

### 深色主题

```typescript
<PhonePreview
  recipientName="张三"
  recipientAvatar="/avatar.jpg"
  messages={messages}
  deviceTime="21:30"
  timeFormat="24"
  mode="dark"
  batteryPercentage={65}
/>
```

### 电量配置

```typescript
// 正常电量
<PhonePreview
  recipientName="联系人"
  recipientAvatar={null}
  messages={messages}
  deviceTime="9:41"
  timeFormat="12"
  mode="light"
  batteryPercentage={85}
/>

// 低电量警告（≤20%时显示红色）
<PhonePreview
  recipientName="联系人"
  recipientAvatar={null}
  messages={messages}
  deviceTime="9:41"
  timeFormat="12"
  mode="light"
  batteryPercentage={15}
/>

// 极低电量
<PhonePreview
  recipientName="联系人"
  recipientAvatar={null}
  messages={messages}
  deviceTime="9:41"
  timeFormat="12"
  mode="light"
  batteryPercentage={5}
/>
```

## 界面结构

### 1. Dynamic Island 状态栏
- **位置**: 顶部胶囊形状区域
- **内容**: 时间、信号、WiFi、电池图标（支持可配置电量百分比）
- **特点**: iPhone 15 系列设计风格，真实的电池状态显示

### 2. 聊天头部
- **返回按钮**: 左侧箭头图标
- **联系人信息**: 头像、姓名、在线状态
- **操作按钮**: 电话和视频通话图标

### 3. 消息区域
- **滚动容器**: 支持消息列表滚动
- **时间戳**: 顶部显示当前时间
- **消息气泡**: 使用 MessageBubble 组件渲染

### 4. 输入区域
- **加号按钮**: 左侧功能按钮
- **输入框**: 消息输入提示
- **发送按钮**: 右侧圆形按钮
- **Home Indicator**: 底部指示条

## 尺寸规格

```typescript
// iPhone 界面尺寸
const PHONE_DIMENSIONS = {
  width: 316,    // 宽度（px）
  height: 684,   // 高度（px）
  borderRadius: 32, // 圆角（px）
};

// 响应式缩放
// 小屏幕设备自动缩放至 80%
```

## 主题配置

### 浅色主题
```css
background: #FFFFFF
border: #F3F4F6
text: #000000
```

### 深色主题
```css
background: #000000
border: #374151
text: #FFFFFF
```

## 头像处理

### 自定义头像
```typescript
// 使用用户上传的头像
recipientAvatar="/path/to/avatar.jpg"
```

### 默认头像
```typescript
// 使用渐变背景和首字母
recipientAvatar={null}
// 自动显示联系人姓名的首字母
```

## 时间格式

### 12小时制
```typescript
timeFormat="12"
deviceTime="9:41"
// 显示: 9:41 AM
```

### 24小时制
```typescript
timeFormat="24"
deviceTime="21:30"
// 显示: 21:30
```

## 性能优化

1. **虚拟滚动**: 大量消息时的性能优化
2. **图片懒加载**: 头像和消息图片的延迟加载
3. **内存管理**: 组件卸载时清理资源
4. **渲染优化**: 使用 React.memo 减少重渲染

## 截图功能

组件专门优化了 html2canvas 兼容性：

```typescript
// 使用 forwardRef 支持截图
const phoneRef = useRef<HTMLDivElement>(null);

<PhonePreview
  ref={phoneRef}
  {...props}
/>

// 截图时使用
await html2canvas(phoneRef.current);
```

## 注意事项

1. **图片资源**: 确保头像图片可访问
2. **消息数量**: 建议单次渲染不超过 100 条消息
3. **主题切换**: 切换主题时会重新渲染所有子组件
4. **时间同步**: deviceTime 需要手动更新

## 相关组件

- [`MessageBubble`](./MessageBubble.md) - 消息气泡组件
- [`DynamicIsland`](./DynamicIsland.md) - 状态栏组件
- [`MessageInput`](./MessageInput.md) - 消息输入组件

## 自定义扩展

### 添加新的状态栏图标
```typescript
// 在 DynamicIsland 组件中添加新图标
// 修改 components/device/DynamicIsland.tsx
```

### 自定义聊天头部
```typescript
// 修改聊天头部样式和功能
// 在 PhonePreview 组件的头部区域添加自定义内容
```
