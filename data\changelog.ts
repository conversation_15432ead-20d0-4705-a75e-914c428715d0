import { ChangelogEntry, UpdateTypeConfig } from '../types/changelog';

/**
 * Changelog data
 * Sorted in reverse chronological order (newest first)
 */
export const changelogEntries: ChangelogEntry[] = [
  {
    id: '2024-01-30-v2.5.0',
    date: '2024-01-30T16:00:00Z',
    version: 'v2.5.0',
    type: 'feature',
    title: 'Smart Battery Management System',
    description: 'Introduced comprehensive battery level adjustment functionality with intelligent validation, visual feedback, and authentic iPhone battery icon rendering.',
    details: [
      'Added configurable battery percentage input (0-100%) with real-time validation',
      'Implemented BatteryIcon component with authentic iPhone battery design',
      'Added low battery warning display (red color for ≤20% battery levels)',
      'Enhanced PhonePreview component with battery percentage support',
      'Added intelligent battery input validation with error handling',
      'Implemented battery state management with automatic reset functionality',
      'Added battery level synchronization across all preview components',
      'Enhanced status bar with realistic battery icon rendering',
      'Added battery percentage bounds checking and user feedback',
      'Optimized battery icon SVG for pixel-perfect iPhone authenticity'
    ]
  },
  {
    id: '2024-01-25-v2.4.0',
    date: '2024-01-25T14:00:00Z',
    version: 'v2.4.0',
    type: 'feature',
    title: 'Advanced Time Management System',
    description: 'Introduced comprehensive time adjustment functionality with intelligent input management, format validation, and enhanced date picker capabilities.',
    details: [
      'Added useTimeInput Hook for complete time state management with validation',
      'Implemented intelligent time input masking and automatic formatting',
      'Enhanced date picker with editable year, month, day, hour, and minute fields',
      'Added boundary value validation to prevent invalid time inputs',
      'Improved 12/24-hour format switching with real-time validation',
      'Optimized time display formatting for iPhone status bar authenticity',
      'Added comprehensive time utility functions (formatDisplayTime, validateTimeFormat, formatTimeInput)',
      'Enhanced MessageInput component with advanced time selection capabilities',
      'Implemented click-outside-to-close functionality for date picker',
      'Added real-time time format conversion and error handling'
    ]
  },
  {
    id: '2024-01-20-v2.3.0',
    date: '2024-01-20T10:00:00Z',
    version: 'v2.3.0',
    type: 'feature',
    title: 'Real-time Preview and Image Upload Features',
    description: 'Introduced WYSIWYG real-time preview system and comprehensive image upload functionality, significantly enhancing user experience.',
    details: [
      'Implemented WYSIWYG real-time preview with instant content synchronization to preview area',
      'Added image upload functionality supporting JPEG, PNG, GIF, WebP formats',
      'Optimized role indicator system supporting sender, receiver, and system message types',
      'Improved date picker interaction with hover trigger and click-outside-to-close',
      'Refactored message tail implementation using CSS pseudo-elements for enhanced authenticity',
      'Added message status indicators (delivered, failed, read)',
      'Enhanced image preview functionality with mixed text and image content support'
    ]
  },
  {
    id: '2024-01-18-v2.2.0',
    date: '2024-01-18T14:30:00Z',
    version: 'v2.2.0',
    type: 'improvement',
    title: 'MessageInput Component Refactoring and Enhancement',
    description: 'Comprehensive refactoring of MessageInput component, providing more powerful message management features and better user experience.',
    details: [
      'Refactored MessageInput component architecture with multi-message management support',
      'Added role switching functionality with colored dot indicators',
      'Implemented image upload and preview features',
      'Optimized memory management with automatic image URL cleanup',
      'Added real-time character counting and input validation',
      'Improved responsive design and mobile experience',
      'Integrated notification system for user feedback'
    ]
  },
  {
    id: '2024-01-15-v2.1.0',
    date: '2024-01-15T10:00:00Z',
    version: 'v2.1.0',
    type: 'feature',
    title: 'iPhone Status Bar Time Display Optimization',
    description: 'Fixed 12-hour format status bar time display by removing AM/PM indicators to better match authentic iPhone interface.',
    details: [
      'Status bar time no longer displays AM/PM in 12-hour format',
      'Maintained normal display format for 24-hour format',
      'Updated related documentation and test cases',
      'Enhanced interface authenticity and user experience'
    ]
  },
  {
    id: '2024-01-10-v2.0.0',
    date: '2024-01-10T14:30:00Z',
    version: 'v2.0.0',
    type: 'feature',
    title: 'Code Architecture Refactoring and Modularization',
    description: 'Completed large-scale code refactoring, splitting the 1053-line main file into multiple focused modules, significantly improving code maintainability.',
    details: [
      'Reduced main file from 1053 lines to approximately 430 lines',
      'Created 6 new module files',
      'Introduced TypeScript type safety',
      'Implemented component-based design',
      'Added custom Hooks',
      'Optimized performance and development experience'
    ]
  },
  {
    id: '2024-01-05-v1.8.0',
    date: '2024-01-05T16:45:00Z',
    version: 'v1.8.0',
    type: 'feature',
    title: 'Message Input Interface Enhancement',
    description: 'Added multi-line message input, role indicators, image upload, and real-time preview functionality.',
    details: [
      'Support for multi-line text input',
      'Added colored role indicators',
      'Integrated image upload and preview functionality',
      'Implemented WYSIWYG real-time preview',
      'Optimized mobile input experience'
    ]
  },
  {
    id: '2024-01-01-v1.7.0',
    date: '2024-01-01T12:00:00Z',
    version: 'v1.7.0',
    type: 'improvement',
    title: 'iPhone Interface Design Upgrade',
    description: 'Comprehensive upgrade of iPhone interface design with support for iPhone 15 series and iOS 17/18 standards.',
    details: [
      'Updated to iPhone 15 series design standards',
      'Support for Dynamic Island status bar',
      'Optimized message bubble styling',
      'Improved light/dark theme switching',
      'Enhanced interface authenticity'
    ]
  },
  {
    id: '2023-12-25-v1.6.0',
    date: '2023-12-25T09:15:00Z',
    version: 'v1.6.0',
    type: 'feature',
    title: 'Image Message Support',
    description: 'Added image message functionality with support for uploading, previewing, and downloading conversation interfaces containing images.',
    details: [
      'Support for JPG, PNG, WebP format images',
      'Automatic image compression and optimization',
      'Image display within message bubbles',
      'High-quality image download functionality'
    ]
  },
  {
    id: '2023-12-20-v1.5.0',
    date: '2023-12-20T11:30:00Z',
    version: 'v1.5.0',
    type: 'improvement',
    title: 'Performance Optimization and User Experience Enhancement',
    description: 'Comprehensive application performance optimization, improving loading speed and interaction responsiveness.',
    details: [
      'Optimized component rendering performance',
      'Reduced unnecessary re-renders',
      'Improved image processing speed',
      'Enhanced mobile experience',
      'Optimized memory usage'
    ]
  },
  {
    id: '2023-12-15-v1.4.0',
    date: '2023-12-15T15:20:00Z',
    version: 'v1.4.0',
    type: 'feature',
    title: 'Custom Time Format',
    description: 'Added 12/24-hour time format switching functionality with support for custom device time display.',
    details: [
      'Support for 12-hour and 24-hour format switching',
      'Custom device time settings',
      'Time format validation and error prompts',
      'Input masking and automatic formatting'
    ]
  },
  {
    id: '2023-12-10-v1.3.0',
    date: '2023-12-10T13:45:00Z',
    version: 'v1.3.0',
    type: 'security',
    title: 'Security Enhancement',
    description: 'Strengthened application security including input validation, XSS protection, and secure data processing.',
    details: [
      'Input content security validation',
      'XSS attack protection',
      'File upload security checks',
      'Sensitive data processing optimization'
    ]
  },
  {
    id: '2023-12-05-v1.2.0',
    date: '2023-12-05T10:10:00Z',
    version: 'v1.2.0',
    type: 'fix',
    title: 'Critical Issue Fixes',
    description: 'Fixed multiple critical issues affecting user experience and improved application stability.',
    details: [
      'Fixed message deletion functionality bug',
      'Resolved avatar upload failure issue',
      'Fixed styling issues during theme switching',
      'Optimized download functionality compatibility'
    ]
  },
  {
    id: '2023-12-01-v1.1.0',
    date: '2023-12-01T08:00:00Z',
    version: 'v1.1.0',
    type: 'feature',
    title: 'Light/Dark Theme Support',
    description: 'Added light/dark theme switching functionality, providing better visual experience and personalization options.',
    details: [
      'Support for light and dark themes',
      'Automatic system theme preference adaptation',
      'Smooth theme switching animations',
      'Theme adaptation for all components'
    ]
  },
  {
    id: '2023-11-25-v1.0.0',
    date: '2023-11-25T12:00:00Z',
    version: 'v1.0.0',
    type: 'feature',
    title: 'Official Project Launch',
    description: 'Fake Text Message Generator officially launched! Providing complete iPhone Messages interface simulation functionality.',
    details: [
      'Authentic iPhone Messages interface',
      'Custom avatars and contact names',
      'Multi-role message support',
      'High-quality PNG image export',
      'Responsive design',
      'SEO optimization and analytics integration'
    ]
  }
];

/**
 * Update type configurations
 */
export const updateTypeConfigs: UpdateTypeConfig[] = [
  {
    type: 'feature',
    label: 'Feature',
    icon: 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z',
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
    count: 0 // Will be calculated at runtime
  },
  {
    type: 'improvement',
    label: 'Improvement',
    icon: 'M13 10V3L4 14h7v7l9-11h-7z',
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    count: 0
  },
  {
    type: 'fix',
    label: 'Fix',
    icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
    color: 'text-orange-600',
    bgColor: 'bg-orange-100',
    count: 0
  },
  {
    type: 'security',
    label: 'Security',
    icon: 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z',
    color: 'text-red-600',
    bgColor: 'bg-red-100',
    count: 0
  }
];
