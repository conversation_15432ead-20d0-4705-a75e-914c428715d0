'use client';

import React from 'react';
import Link from 'next/link';
import Header from '../../components/layout/Header';
import Footer from '../../components/layout/Footer';
import Timeline from '../../components/changelog/Timeline';
import { changelogEntries } from '../../data/changelog';
import { useChangelog } from '../../hooks/useChangelog';

/**
 * 更新历史页面
 * 展示项目的完整更新时间轴
 */
export default function ChangelogPage() {
  const {
    entries,
    filteredEntries,
    stats,
    filter,
    searchQuery,
    handleFilterChange,
    handleSearchChange,
    handleToggleExpand,
    expandAll,
    collapseAll,
    clearFilters,
    hasFilters,
    isEmpty,
    totalCount,
    filteredCount
  } = useChangelog(changelogEntries);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Page Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Breadcrumb Navigation */}
          <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-4">
            <Link
              href="/"
              className="hover:text-blue-600 transition-colors"
            >
              Home
            </Link>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
            <span className="text-gray-900 font-medium">Changelog</span>
          </nav>

          {/* Page Title */}
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Changelog
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl">
                Track all feature updates, improvements, and fixes for the fake text message generator. Stay informed about our product development journey.
              </p>
            </div>

            {/* Back Button */}
            <Link
              href="/"
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Home
            </Link>
          </div>

          {/* Statistics */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-blue-600">
                {stats.totalUpdates}
              </div>
              <div className="text-sm text-blue-600">
                Total Updates
              </div>
            </div>

            <div className="bg-green-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-green-600">
                {stats.typeStats.feature || 0}
              </div>
              <div className="text-sm text-green-600">
                New Features
              </div>
            </div>

            <div className="bg-orange-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-orange-600">
                {stats.typeStats.improvement || 0}
              </div>
              <div className="text-sm text-orange-600">
                Improvements
              </div>
            </div>

            <div className="bg-purple-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-purple-600">
                {stats.latestUpdate ? stats.latestUpdate.toLocaleDateString('en-US') : 'Unknown'}
              </div>
              <div className="text-sm text-purple-600">
                Latest Update
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Action Buttons */}
        <div className="mb-6 flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            {hasFilters && (
              <button
                onClick={clearFilters}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
                Clear Filters
              </button>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={expandAll}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200"
            >
              Expand All
            </button>
            <button
              onClick={collapseAll}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200"
            >
              Collapse All
            </button>
          </div>
        </div>

        <Timeline
          entries={entries}
          filter={filter}
          searchQuery={searchQuery}
          onFilterChange={handleFilterChange}
          onSearchChange={handleSearchChange}
          onToggleExpand={handleToggleExpand}
        />
      </main>

      <Footer />
    </div>
  );
}
