import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import { NotificationProvider } from '../components/ui/notification';
import AnalyticsProvider from '../components/analytics/AnalyticsProvider';
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  colorScheme: 'light dark',
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#3B82F6' },
    { media: '(prefers-color-scheme: dark)', color: '#1E40AF' },
  ],
};

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://faketextmessage.xyz'),
  title: "Fake Text Message Generator - Create Realistic SMS Conversations",
  description: "Create realistic fake text messages and SMS conversations with advanced time management, smart battery control, real-time preview, and image upload. Perfect for text story videos, mockups, and creative projects. No email required, instant download.",
  keywords: [
    "fake text message",
    "fake text message iphone",
    "fake text",
    "text message generator",
    "fake text generator",
    "fake SMS generator",
    "text message simulator",
    "mock text conversation",
    "SMS mockup",
    "text story generator",
    "fake chat generator",
    "message simulator",
    "time management",
    "date picker",
    "real-time preview",
    "WYSIWYG editor",
    "iPhone Messages interface",
    "iOS 17 Messages",
    "message timestamp",
    "12 hour format",
    "24 hour format",
    "battery level",
    "battery percentage",
    "iPhone battery",
    "battery management",
    "low battery warning",
    "device configuration"
  ],
  authors: [{ name: "Fake Text Generator" }],
  creator: "Fake Text Generator",
  publisher: "Fake Text Generator",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: process.env.NEXT_PUBLIC_SITE_URL || 'https://faketextmessage.xyz',
    siteName: 'Fake Text Message Generator',
    title: 'Fake Text Message Generator - Create Realistic SMS Conversations',
    description: 'Create realistic fake text messages and SMS conversations for free. Perfect for text story videos, mockups, and creative projects.',
    images: [
      {
        url: '/og-image.svg',
        width: 1200,
        height: 630,
        alt: 'Fake Text Message Generator - Create realistic SMS conversations for free',
        type: 'image/svg+xml',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Fake Text Message Generator - Create Realistic SMS Conversations',
    description: 'Create realistic fake text messages and SMS conversations for free. Perfect for text story videos, mockups, and creative projects.',
    images: ['/og-image.svg'],
    creator: '@faketextgen',
    site: '@faketextgen',
  },
  alternates: {
    canonical: process.env.NEXT_PUBLIC_SITE_URL || 'https://faketextmessage.xyz',
  },
  category: 'Technology',
  classification: 'Web Application',
  referrer: 'origin-when-cross-origin',

  verification: {
    google: process.env.NEXT_PUBLIC_GSC_VERIFICATION,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* Favicon 配置 - 现代最佳实践 */}
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="icon" href="/favicon-fallback.svg" type="image/svg+xml" />
        <link rel="icon" href="/favicon-16x16.svg" sizes="16x16" type="image/svg+xml" />
        <link rel="icon" href="/favicon-32x32.svg" sizes="32x32" type="image/svg+xml" />
        <link rel="icon" href="/favicon-192x192.svg" sizes="192x192" type="image/svg+xml" />
        <link rel="icon" href="/favicon-512x512.svg" sizes="512x512" type="image/svg+xml" />

        {/* Apple 设备图标 */}
        <link rel="apple-touch-icon" href="/apple-touch-icon.svg" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.svg" />

        {/* Web App Manifest */}
        <link rel="manifest" href="/manifest.json" />

        {/* Web App 配置 */}
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#3B82F6" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Fake Text Generator" />

        {/* Microsoft 瓦片配置 */}
        <meta name="msapplication-TileColor" content="#3B82F6" />
        <meta name="msapplication-config" content="none" />

        {/* 安全和性能优化 */}
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />

        {/* Google Search Console验证 - 有验证码时显示 */}
        {process.env.NEXT_PUBLIC_GSC_VERIFICATION && (
          <meta
            name="google-site-verification"
            content={process.env.NEXT_PUBLIC_GSC_VERIFICATION}
          />
        )}
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <NotificationProvider>
          <AnalyticsProvider>
            {children}
          </AnalyticsProvider>
        </NotificationProvider>
      </body>
    </html>
  );
}
