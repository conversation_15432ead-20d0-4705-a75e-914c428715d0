'use client';

import React, { createContext, useContext, useReducer, useCallback, useRef } from 'react';
import NotificationRenderer from './NotificationRenderer';
import {
  NotificationConfig,
  NotificationState,
  NotificationContextType,
  ToastConfig,
  ModalConfig,
  ConfirmOptions,
  AlertOptions,
  DEFAULT_TOAST_CONFIG,
  DEFAULT_MODAL_CONFIG,
  MODAL_TYPE_STATUS_MAP,
} from './types';

// Actions
type NotificationAction =
  | { type: 'ADD_NOTIFICATION'; payload: NotificationConfig }
  | { type: 'REMOVE_NOTIFICATION'; payload: string }
  | { type: 'CLEAR_ALL' }
  | { type: 'PROCESS_QUEUE' };

// Initial state
const initialState: NotificationState = {
  notifications: [],
  queue: [],
};

// Reducer
function notificationReducer(state: NotificationState, action: NotificationAction): NotificationState {
  switch (action.type) {
    case 'ADD_NOTIFICATION':
      return {
        ...state,
        notifications: [...state.notifications, action.payload],
      };
    
    case 'REMOVE_NOTIFICATION':
      return {
        ...state,
        notifications: state.notifications.filter(n => n.id !== action.payload),
      };
    
    case 'CLEAR_ALL':
      return {
        ...state,
        notifications: [],
        queue: [],
      };
    
    case 'PROCESS_QUEUE':
      if (state.queue.length === 0) return state;
      
      const [next, ...remainingQueue] = state.queue;
      return {
        notifications: [...state.notifications, next],
        queue: remainingQueue,
      };
    
    default:
      return state;
  }
}

// Context
const NotificationContext = createContext<NotificationContextType | null>(null);

// Provider component
interface NotificationProviderProps {
  children: React.ReactNode;
  maxNotifications?: number;
}

export function NotificationProvider({ 
  children, 
  maxNotifications = 5 
}: NotificationProviderProps) {
  const [state, dispatch] = useReducer(notificationReducer, initialState);
  const idCounter = useRef(0);
  const pendingPromises = useRef<Map<string, { resolve: (value: any) => void; reject: (reason?: any) => void }>>(new Map());

  // Generate unique ID
  const generateId = useCallback(() => {
    idCounter.current += 1;
    return `notification-${Date.now()}-${idCounter.current}`;
  }, []);

  // Add notification with queue management
  const addNotification = useCallback((config: NotificationConfig) => {
    if (state.notifications.length >= maxNotifications) {
      // Add to queue if max notifications reached
      dispatch({ type: 'ADD_NOTIFICATION', payload: config });
    } else {
      dispatch({ type: 'ADD_NOTIFICATION', payload: config });
    }

    // Auto-remove after duration
    if (config.duration && config.duration > 0) {
      setTimeout(() => {
        dispatch({ type: 'REMOVE_NOTIFICATION', payload: config.id });
      }, config.duration);
    }

    return config.id;
  }, [state.notifications.length, maxNotifications]);

  // Show toast
  const showToast = useCallback((options: Omit<ToastConfig, 'id' | 'type'>) => {
    const config: ToastConfig = {
      ...DEFAULT_TOAST_CONFIG,
      ...options,
      id: generateId(),
      type: 'toast',
    };

    return addNotification(config);
  }, [generateId, addNotification]);

  // Show modal
  const showModal = useCallback((options: Omit<ModalConfig, 'id' | 'type'>) => {
    const config: ModalConfig = {
      ...DEFAULT_MODAL_CONFIG,
      ...options,
      id: generateId(),
      type: 'modal',
    };

    return addNotification(config);
  }, [generateId, addNotification]);

  // Show confirm dialog
  const showConfirm = useCallback((options: ConfirmOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      const id = generateId();
      
      const config: ModalConfig = {
        ...DEFAULT_MODAL_CONFIG,
        id,
        type: 'modal',
        modalType: 'confirm',
        status: 'confirm',
        title: options.title,
        message: options.message,
        duration: 0, // Don't auto-close
        buttons: [
          {
            text: options.cancelText || 'Cancel',
            variant: 'secondary',
            onClick: () => {
              resolve(false);
              dispatch({ type: 'REMOVE_NOTIFICATION', payload: id });
            },
          },
          {
            text: options.confirmText || 'Confirm',
            variant: options.variant || 'primary',
            onClick: () => {
              resolve(true);
              dispatch({ type: 'REMOVE_NOTIFICATION', payload: id });
            },
          },
        ],
        onClose: () => {
          resolve(false);
          dispatch({ type: 'REMOVE_NOTIFICATION', payload: id });
        },
        width: options.width,
      };

      addNotification(config);
    });
  }, [generateId, addNotification]);

  // Show alert dialog
  const showAlert = useCallback((options: AlertOptions): Promise<void> => {
    return new Promise((resolve) => {
      const id = generateId();
      
      const config: ModalConfig = {
        ...DEFAULT_MODAL_CONFIG,
        id,
        type: 'modal',
        modalType: options.status || 'info',
        status: MODAL_TYPE_STATUS_MAP[options.status || 'info'],
        title: options.title,
        message: options.message,
        duration: 0, // Don't auto-close
        buttons: [
          {
            text: options.confirmText || 'OK',
            variant: 'primary',
            onClick: () => {
              resolve();
              dispatch({ type: 'REMOVE_NOTIFICATION', payload: id });
            },
          },
        ],
        onClose: () => {
          resolve();
          dispatch({ type: 'REMOVE_NOTIFICATION', payload: id });
        },
        width: options.width,
      };

      addNotification(config);
    });
  }, [generateId, addNotification]);

  // Hide notification
  const hide = useCallback((id: string) => {
    // Resolve any pending promises
    const promise = pendingPromises.current.get(id);
    if (promise) {
      promise.resolve(false); // Default to false for confirms
      pendingPromises.current.delete(id);
    }
    
    dispatch({ type: 'REMOVE_NOTIFICATION', payload: id });
  }, []);

  // Hide all notifications
  const hideAll = useCallback(() => {
    // Resolve all pending promises
    pendingPromises.current.forEach(promise => {
      promise.resolve(false);
    });
    pendingPromises.current.clear();
    
    dispatch({ type: 'CLEAR_ALL' });
  }, []);

  const contextValue: NotificationContextType = {
    state,
    showToast,
    showModal,
    showConfirm,
    showAlert,
    hide,
    hideAll,
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
      <NotificationRenderer />
    </NotificationContext.Provider>
  );
}

// Hook to use notification context
export function useNotificationContext(): NotificationContextType {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotificationContext must be used within a NotificationProvider');
  }
  return context;
}

export default NotificationProvider;
