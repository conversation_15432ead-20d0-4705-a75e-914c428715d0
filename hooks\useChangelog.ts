import { useState, useMemo, useCallback } from 'react';
import { ChangelogEntry, FilterType } from '../types/changelog';
import { filterEntries, calculateTypeCounts, getUpdateStats } from '../utils/changelog';

/**
 * 更新历史管理Hook
 * 提供筛选、搜索、展开/收起等功能
 */
export const useChangelog = (initialEntries: ChangelogEntry[]) => {
  // 状态管理
  const [entries, setEntries] = useState<ChangelogEntry[]>(
    initialEntries.map(entry => ({ ...entry, isExpanded: false }))
  );
  const [filter, setFilter] = useState<FilterType>('all');
  const [searchQuery, setSearchQuery] = useState('');

  // 筛选后的条目
  const filteredEntries = useMemo(() => {
    return filterEntries(entries, filter, searchQuery);
  }, [entries, filter, searchQuery]);

  // 统计信息
  const stats = useMemo(() => {
    return getUpdateStats(entries);
  }, [entries]);

  // 类型计数
  const typeCounts = useMemo(() => {
    return calculateTypeCounts(entries);
  }, [entries]);

  // 处理筛选变更
  const handleFilterChange = useCallback((newFilter: FilterType) => {
    setFilter(newFilter);
  }, []);

  // 处理搜索变更
  const handleSearchChange = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  // 处理展开/收起详情
  const handleToggleExpand = useCallback((id: string) => {
    setEntries(prevEntries =>
      prevEntries.map(entry =>
        entry.id === id
          ? { ...entry, isExpanded: !entry.isExpanded }
          : entry
      )
    );
  }, []);

  // 展开所有条目
  const expandAll = useCallback(() => {
    setEntries(prevEntries =>
      prevEntries.map(entry => ({ ...entry, isExpanded: true }))
    );
  }, []);

  // 收起所有条目
  const collapseAll = useCallback(() => {
    setEntries(prevEntries =>
      prevEntries.map(entry => ({ ...entry, isExpanded: false }))
    );
  }, []);

  // 清除所有筛选
  const clearFilters = useCallback(() => {
    setFilter('all');
    setSearchQuery('');
  }, []);

  // 重置到初始状态
  const reset = useCallback(() => {
    setEntries(initialEntries.map(entry => ({ ...entry, isExpanded: false })));
    setFilter('all');
    setSearchQuery('');
  }, [initialEntries]);

  return {
    // 数据
    entries,
    filteredEntries,
    stats,
    typeCounts,
    
    // 筛选状态
    filter,
    searchQuery,
    
    // 操作函数
    handleFilterChange,
    handleSearchChange,
    handleToggleExpand,
    expandAll,
    collapseAll,
    clearFilters,
    reset,
    
    // 计算属性
    hasFilters: filter !== 'all' || searchQuery.trim() !== '',
    isEmpty: filteredEntries.length === 0,
    totalCount: entries.length,
    filteredCount: filteredEntries.length
  };
};
