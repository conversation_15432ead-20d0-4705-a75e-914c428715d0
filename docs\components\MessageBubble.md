# MessageBubble 组件文档

## 概述

`MessageBubble` 是一个高度还原 iOS Messages 应用的消息气泡组件，支持文本消息、图片消息和混合内容显示。

## 功能特性

- ✅ **iOS 17/18 风格设计** - 完全还原最新 iOS Messages 的视觉效果
- ✅ **多消息类型** - 支持用户消息、接收消息、系统消息
- ✅ **多媒体支持** - 支持纯图片、纯文本、图文混合消息
- ✅ **CSS 伪元素尾巴** - 使用 :before 和 :after 伪元素实现真实的消息气泡尾巴
- ✅ **主题适配** - 支持浅色和深色主题，动态颜色变量
- ✅ **智能时间显示** - 可选的时间戳显示，支持自定义格式
- ✅ **状态指示** - 支持消息状态显示（已送达、失败、已读）
- ✅ **响应式布局** - 完美适配移动端和桌面端

## 组件接口

```typescript
interface MessageBubbleProps {
  message: Message;              // 消息对象
  recipientName: string;         // 联系人姓名
  recipientAvatar: string | null; // 联系人头像
  mode: ThemeMode;              // 主题模式
  timeFormat: TimeFormat;        // 时间格式
  showTime?: boolean;           // 是否显示时间（可选）
  isFirstMessage?: boolean;     // 是否是第一个消息（可选）
  shouldShowTimestamp?: boolean; // 是否应该显示时间戳（可选）
}

interface Message {
  id: string;                   // 唯一标识符
  sender: 'user' | 'recipient' | 'system'; // 发送者类型
  content: string;              // 消息内容
  timestamp?: string;           // 时间戳
  status?: 'default' | 'delivered' | 'failed' | 'read'; // 消息状态
  imageUrl?: string;            // 图片URL
  method?: 'data' | 'image' | 'unknown'; // 消息类型
}
```

## 使用示例

### 基础文本消息

```typescript
import MessageBubble from '../components/ui/MessageBubble';

// 用户发送的消息
<MessageBubble
  message={{
    id: '1',
    sender: 'user',
    content: '你好，最近怎么样？',
    timestamp: '10:30',
    status: 'delivered'
  }}
  recipientName="张三"
  recipientAvatar="/avatar.jpg"
  mode="light"
  timeFormat="12"
  showTime={false}
/>

// 接收到的消息
<MessageBubble
  message={{
    id: '2',
    sender: 'recipient',
    content: '很好，谢谢！你呢？',
    timestamp: '10:31',
    status: 'read'
  }}
  recipientName="张三"
  recipientAvatar="/avatar.jpg"
  mode="light"
  timeFormat="12"
  showTime={false}
/>
```

### 图片消息

```typescript
// 纯图片消息
<MessageBubble
  message={{
    id: '3',
    sender: 'user',
    content: '',
    imageUrl: '/photo.jpg',
    timestamp: '10:32',
    status: 'delivered'
  }}
  recipientName="张三"
  recipientAvatar="/avatar.jpg"
  mode="light"
  timeFormat="12"
  showTime={false}
/>

// 图文混合消息
<MessageBubble
  message={{
    id: '4',
    sender: 'user',
    content: '看看这张照片！',
    imageUrl: '/photo.jpg',
    timestamp: '10:33',
    status: 'delivered'
  }}
  recipientName="张三"
  recipientAvatar="/avatar.jpg"
  mode="light"
  timeFormat="12"
  showTime={true}
/>
```

### 系统消息

```typescript
// 系统通知消息
<MessageBubble
  message={{
    id: '5',
    sender: 'system',
    content: '张三已加入群聊',
    timestamp: '10:34',
    status: 'default'
  }}
  recipientName="张三"
  recipientAvatar="/avatar.jpg"
  mode="light"
  timeFormat="12"
  showTime={false}
/>
```

## 主题支持

组件支持浅色和深色两种主题：

```typescript
// 浅色主题
<MessageBubble mode="light" {...otherProps} />

// 深色主题
<MessageBubble mode="dark" {...otherProps} />
```

### 主题色彩规范

| 消息类型 | 浅色模式 | 深色模式 |
|---------|---------|---------|
| 用户消息 | #007AFF (蓝色) | #007AFF (蓝色) |
| 接收消息 | #E5E5EA (浅灰) | #262628 (深灰) |
| 系统消息 | #34C759 (绿色) | #34C759 (绿色) |

## 时间格式

支持 12 小时制和 24 小时制：

```typescript
// 12小时制（显示 AM/PM）
<MessageBubble timeFormat="12" {...otherProps} />

// 24小时制
<MessageBubble timeFormat="24" {...otherProps} />
```

## CSS 伪元素尾巴实现

组件使用 CSS 伪元素技术实现真实的消息气泡尾巴效果：

### 技术实现

```css
/* 发送消息尾巴（右侧） */
.message-bubble-user::before {
  content: '';
  position: absolute;
  bottom: 0;
  right: -6px;
  width: 20px;
  height: 20px;
  background: var(--bubble-color);
  border-radius: 0 0 16px 0;
}

.message-bubble-user::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: -10px;
  width: 10px;
  height: 20px;
  background: var(--background-color);
  border-radius: 0 0 10px 0;
}

/* 接收消息尾巴（左侧） */
.message-bubble-recipient::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: -6px;
  width: 20px;
  height: 20px;
  background: var(--bubble-color);
  border-radius: 0 0 0 16px;
}

.message-bubble-recipient::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: -10px;
  width: 10px;
  height: 20px;
  background: var(--background-color);
  border-radius: 0 0 0 10px;
}
```

### 主题适配

```css
/* CSS 变量支持主题切换 */
:root {
  --user-bubble-color: #007AFF;
  --recipient-bubble-color-light: #E5E5EA;
  --recipient-bubble-color-dark: #262628;
  --background-color-light: #FFFFFF;
  --background-color-dark: #000000;
}
```

## 样式自定义

组件使用内联样式确保与 html2canvas 的兼容性，主要样式包括：

- **气泡圆角**: 14px（符合 iOS 设计规范）
- **字体大小**: 13px（San Francisco 字体）
- **行高**: 18px（最佳可读性）
- **最大宽度**: 198px（适配 iPhone 屏幕）
- **内边距**: 6px 10px（舒适的内容间距）
- **尾巴尺寸**: 20x20px（真实的 iOS 比例）

## 注意事项

1. **图片处理**: 图片会自动调整大小，最大高度 200px
2. **文本换行**: 长文本会自动换行，保持气泡宽度
3. **html2canvas 兼容**: 使用内联样式确保截图功能正常
4. **性能优化**: 组件已优化，支持大量消息的渲染

## 相关组件

- [`PhonePreview`](./PhonePreview.md) - iPhone 预览容器
- [`MessageInput`](./MessageInput.md) - 消息输入组件
- [`DynamicIsland`](./DynamicIsland.md) - 状态栏组件
