import React from 'react';
import { MessageBubbleProps } from '../../types/message';

/**
 * MessageBubble 组件 - iOS 风格的消息气泡
 * 支持文本和图片消息，具有真实的 iOS Messages 外观
 */
const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  recipientName,
  recipientAvatar,
  mode,
  timeFormat,
  showTime = false,
  isFirstMessage = false,
  shouldShowTimestamp = true
}) => {
  const isUser = message.sender === 'user';
  const isSystem = message.sender === 'system';

  // 格式化个别消息时间戳（支持自定义时间戳或当前时间）
  const formatMessageTimestamp = (timestamp?: string) => {
    try {
      // 如果有自定义时间戳，使用自定义时间；否则使用当前时间
      const date = timestamp && timestamp.trim() !== ''
        ? new Date(timestamp)
        : new Date();

      if (isNaN(date.getTime())) {
        // 如果自定义时间戳无效，回退到当前时间
        const fallbackDate = new Date();
        return formatDateToTimestamp(fallbackDate);
      }

      return formatDateToTimestamp(date);
    } catch (error) {
      console.warn('Invalid timestamp:', timestamp);
      // 出错时回退到当前时间
      return formatDateToTimestamp(new Date());
    }
  };

  // 将Date对象格式化为时间戳字符串
  const formatDateToTimestamp = (date: Date) => {
    const now = new Date();
    const timeStr = date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: timeFormat === '12'
    });

    // 检查是否是今天
    const isToday = date.toDateString() === now.toDateString();
    if (isToday) {
      return `Today ${timeStr}`;
    }

    // 检查是否是昨天
    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    const isYesterday = date.toDateString() === yesterday.toDateString();
    if (isYesterday) {
      return `Yesterday ${timeStr}`;
    }

    // 检查是否在本周内（周日开始）
    const startOfWeek = new Date(now);
    const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    startOfWeek.setDate(now.getDate() - dayOfWeek);
    startOfWeek.setHours(0, 0, 0, 0);

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);

    const isThisWeek = date >= startOfWeek && date <= endOfWeek;
    if (isThisWeek) {
      const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });
      return `${dayName} ${timeStr}`;
    }

    // 其他时间：Tue, Jul 1 at 12:21 AM
    const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
    const monthDay = date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
    return `${dayName}, ${monthDay} at ${timeStr}`;
  };

  // 渲染纯图片气泡（无MessageTail，完整圆角）
  const renderImageBubble = () => {
    const showErrorIcon = (isUser || isSystem) && message.status === 'failed';

    return (
      <div className={`flex items-center gap-[6px] ${(isUser || isSystem) ? 'pr-[6px]' : 'pl-[6px]'}`}>
        <div className="relative flex max-w-[198px] break-words rounded-[14px] overflow-hidden">
          <img
            src={message.imageUrl}
            alt="Message attachment"
            className="w-full h-auto block rounded-[14px]"
            style={{
              maxHeight: '200px',
              objectFit: 'cover'
            }}
          />
        </div>
        {/* 错误图标 - 只在发送失败时显示 */}
        {showErrorIcon && (
          <svg
            viewBox="0 0 26 26"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            style={{
              width: '19px',
              height: '19px',
              color: '#FD3B31',
              flexShrink: 0,
              zIndex: 10
            }}
          >
            <path d="M12.7148 25.4395C19.6777 25.4395 25.4395 19.6777 25.4395 12.7246C25.4395 5.76172 19.668 0 12.7051 0C5.75195 0 0 5.76172 0 12.7246C0 19.6777 5.76172 25.4395 12.7148 25.4395ZM12.7148 23.623C6.67969 23.623 1.82617 18.7598 1.82617 12.7246C1.82617 6.67969 6.66992 1.82617 12.7051 1.82617C18.75 1.82617 23.6133 6.67969 23.6133 12.7246C23.6133 18.7598 18.7598 23.623 12.7148 23.623Z" fill="currentColor"></path>
            <path d="M12.6953 14.9902C13.2227 14.9902 13.5352 14.6777 13.5449 14.1016L13.7012 7.39258C13.7109 6.82617 13.2715 6.40625 12.6855 6.40625C12.0898 6.40625 11.6797 6.81641 11.6895 7.38281L11.8359 14.1016C11.8457 14.668 12.1582 14.9902 12.6953 14.9902ZM12.6953 18.9648C13.3691 18.9648 13.9355 18.4277 13.9355 17.7637C13.9355 17.0898 13.3789 16.5625 12.6953 16.5625C12.0215 16.5625 11.4648 17.0996 11.4648 17.7637C11.4648 18.418 12.0312 18.9648 12.6953 18.9648Z" fill="currentColor"></path>
          </svg>
        )}
      </div>
    );
  };

  // 渲染纯文字气泡（使用内联样式，确保html2canvas兼容性）
  const renderTextBubble = () => {
    // 根据消息类型确定样式
    const isRightAligned = isUser || isSystem;
    const tailBackgroundColor = mode === 'dark' ? '#000000' : '#FFFFFF';
    const showErrorIcon = (isUser || isSystem) && message.status === 'failed';

    // 消息气泡颜色
    let bubbleColor: string;
    let textColor: string;

    if (isSystem) {
      bubbleColor = '#34C759'; // 绿色系统消息
      textColor = 'white';
    } else if (isUser) {
      bubbleColor = '#007AFF'; // 蓝色用户消息
      textColor = 'white';
    } else {
      bubbleColor = mode === 'dark' ? '#262628' : '#E5E5EA'; // 灰色接收消息
      textColor = mode === 'dark' ? 'white' : 'black';
    }

    // 容器样式
    const wrapperStyle: React.CSSProperties = {
      display: 'flex',
      flexDirection: 'column',
      gap: '2px',
      alignItems: isRightAligned ? 'flex-end' : 'flex-start',
      marginBottom: '6px'
    };

    // 气泡容器样式
    const bubbleContainerStyle: React.CSSProperties = {
      position: 'relative',
      maxWidth: '198px',
      display: 'flex',
      alignItems: 'center',
      gap: '6px',
      // 与图片消息保持一致的边距
      paddingRight: isRightAligned ? '6px' : '0',
      paddingLeft: isRightAligned ? '0' : '6px'
    };

    // 主气泡样式
    const bubbleStyle: React.CSSProperties = {
      position: 'relative',
      maxWidth: '198px',
      wordWrap: 'break-word',
      lineHeight: '18px',
      padding: '6px 10px',
      borderRadius: '14px',
      backgroundColor: bubbleColor,
      color: textColor,
      fontSize: '13px',
      fontFamily: '"Helvetica Neue", Helvetica, -apple-system, BlinkMacSystemFont, sans-serif',
      overflow: 'visible'
    };

    // 文本样式
    const textStyle: React.CSSProperties = {
      width: '100%',
      whiteSpace: 'pre-wrap',
      fontSize: '13px',
      lineHeight: '16px'
    };

    // 尾巴前景样式（::before效果）
    const tailForegroundStyle: React.CSSProperties = {
      content: '""',
      position: 'absolute',
      bottom: 0,
      height: '18px',
      width: '15px',
      backgroundColor: bubbleColor,
      ...(isRightAligned ? {
        right: '-5px',
        borderBottomLeftRadius: '12px 10px'
      } : {
        left: '-5px',
        borderBottomRightRadius: '12px 10px'
      })
    };

    // 尾巴背景样式（::after效果）
    const tailBackgroundStyle: React.CSSProperties = {
      content: '""',
      position: 'absolute',
      bottom: 0,
      height: '20px',
      width: '20px',
      backgroundColor: tailBackgroundColor,
      ...(isRightAligned ? {
        right: '-20px',
        borderBottomLeftRadius: '8px'
      } : {
        left: '-20px',
        borderBottomRightRadius: '8px'
      })
    };

    return (
      <div style={wrapperStyle}>
        <div style={bubbleContainerStyle}>
          <div style={bubbleStyle}>
            <span style={textStyle}>
              {message.content}
            </span>
            {/* 尾巴前景元素 */}
            <div style={tailForegroundStyle}></div>
            {/* 尾巴背景元素 */}
            <div style={tailBackgroundStyle}></div>
          </div>
          {/* 错误图标 - 只在发送失败时显示 */}
          {showErrorIcon && (
            <svg
              viewBox="0 0 26 26"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              style={{
                width: '19px',
                height: '19px',
                color: '#FD3B31',
                flexShrink: 0,
                zIndex: 10
              }}
            >
              <path d="M12.7148 25.4395C19.6777 25.4395 25.4395 19.6777 25.4395 12.7246C25.4395 5.76172 19.668 0 12.7051 0C5.75195 0 0 5.76172 0 12.7246C0 19.6777 5.76172 25.4395 12.7148 25.4395ZM12.7148 23.623C6.67969 23.623 1.82617 18.7598 1.82617 12.7246C1.82617 6.67969 6.66992 1.82617 12.7051 1.82617C18.75 1.82617 23.6133 6.67969 23.6133 12.7246C23.6133 18.7598 18.7598 23.623 12.7148 23.623Z" fill="currentColor"></path>
              <path d="M12.6953 14.9902C13.2227 14.9902 13.5352 14.6777 13.5449 14.1016L13.7012 7.39258C13.7109 6.82617 13.2715 6.40625 12.6855 6.40625C12.0898 6.40625 11.6797 6.81641 11.6895 7.38281L11.8359 14.1016C11.8457 14.668 12.1582 14.9902 12.6953 14.9902ZM12.6953 18.9648C13.3691 18.9648 13.9355 18.4277 13.9355 17.7637C13.9355 17.0898 13.3789 16.5625 12.6953 16.5625C12.0215 16.5625 11.4648 17.0996 11.4648 17.7637C11.4648 18.418 12.0312 18.9648 12.6953 18.9648Z" fill="currentColor"></path>
            </svg>
          )}
        </div>
      </div>
    );
  };

  // 渲染消息状态指示器
  const renderMessageStatus = () => {
    // 用户发送的消息和系统消息都可以显示状态，但接收方消息不显示状态
    if ((!isUser && !isSystem) || !message.status || message.status === 'default') {
      return null;
    }

    let statusText = '';

    switch (message.status) {
      case 'delivered':
        statusText = 'Delivered';
        break;
      case 'failed':
        statusText = 'Not Delivered';
        break;
      case 'read':
        statusText = 'Read';
        break;
      default:
        return null;
    }

    // 根据状态确定文本颜色（使用内联样式确保html2canvas兼容性）
    let textColor = '#8A898E'; // 默认颜色

    switch (message.status) {
      case 'delivered':
      case 'read':
        textColor = '#8A898E';
        break;
      case 'failed':
        textColor = '#FD3B31';
        break;
    }

    return (
      <div className="flex justify-end text-[9px] font-semibold">
        <span style={{ color: textColor }}>
          {statusText}
        </span>
      </div>
    );
  };

  // 所有消息都显示时间戳：自定义时间戳或当前时间
  const formattedTimestamp = formatMessageTimestamp(message.timestamp);

  return (
    <div className={`flex flex-col gap-[2px] pb-[6px]`}>
      {/* 个别消息时间戳 - 第一个消息不显示（因为已在全局显示），其他消息根据shouldShowTimestamp决定 */}
      {!isFirstMessage && shouldShowTimestamp && (
        <div className="flex justify-center pb-[2px]">
          <span
            className="text-[9px] font-normal text-[#8D8D93] px-[6px]"
            style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif' }}
          >
            {formattedTimestamp}
          </span>
        </div>
      )}

      <div className={`flex flex-col gap-[2px] ${(isUser || isSystem) ? 'items-end' : 'items-start'}`}>
        {/* 图片气泡（如果有图片） */}
        {message.imageUrl && renderImageBubble()}

        {/* 文字气泡（如果有文字） */}
        {message.content && renderTextBubble()}
      </div>

      {/* 消息状态显示 - 只对用户发送的消息显示 */}
      {renderMessageStatus()}

      {/* 保留原有的showTime逻辑，用于全局时间显示 */}
      {showTime && (
        <div className="flex justify-center pb-[6px] pt-[11px] text-center">
          <span className="text-[9px] font-medium text-[#8D8D93]">
            Today {new Date().toLocaleTimeString('en-US', {
              hour: 'numeric',
              minute: '2-digit',
              hour12: timeFormat === '12'
            })}
          </span>
        </div>
      )}
    </div>
  );
};

export default MessageBubble;
