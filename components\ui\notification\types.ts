/**
 * Notification System Types
 * 统一的通知/模态系统类型定义
 */

// 基础通知类型
export type NotificationType = 'toast' | 'modal';

// 通知状态类型（扩展原有ToastStatus）
export type NotificationStatus = 'loading' | 'success' | 'error' | 'warning' | 'info' | 'confirm';

// 模态类型
export type ModalType = 'confirm' | 'alert' | 'info' | 'warning' | 'error' | 'success';

// 按钮配置
export interface ButtonConfig {
  text: string;
  variant: 'primary' | 'secondary' | 'danger';
  onClick: () => void;
}

// 基础通知配置
export interface BaseNotificationConfig {
  id: string;
  type: NotificationType;
  status: NotificationStatus;
  title?: string;
  message: string;
  duration?: number; // 自动关闭时间（毫秒），0表示不自动关闭
  closable?: boolean; // 是否可手动关闭
  onClose?: () => void;
}

// Toast特定配置
export interface ToastConfig extends BaseNotificationConfig {
  type: 'toast';
  position?: 'top-center' | 'top-right' | 'bottom-center' | 'bottom-right';
}

// Modal特定配置
export interface ModalConfig extends BaseNotificationConfig {
  type: 'modal';
  modalType: ModalType;
  buttons?: ButtonConfig[];
  backdrop?: boolean; // 是否显示遮罩
  backdropClosable?: boolean; // 点击遮罩是否关闭
  escClosable?: boolean; // ESC键是否关闭
  width?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}

// 统一的通知配置类型
export type NotificationConfig = ToastConfig | ModalConfig;

// 通知状态管理
export interface NotificationState {
  notifications: NotificationConfig[];
  queue: NotificationConfig[];
}

// Context类型
export interface NotificationContextType {
  state: NotificationState;
  showToast: (config: Omit<ToastConfig, 'id' | 'type'>) => string;
  showModal: (config: Omit<ModalConfig, 'id' | 'type'>) => string;
  showConfirm: (config: ConfirmOptions) => Promise<boolean>;
  showAlert: (config: AlertOptions) => Promise<void>;
  hide: (id: string) => void;
  hideAll: () => void;
}

// 便捷方法的选项类型
export interface ConfirmOptions {
  title?: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'primary' | 'danger';
  width?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}

export interface AlertOptions {
  title?: string;
  message: string;
  confirmText?: string;
  status?: 'info' | 'warning' | 'error' | 'success';
  width?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}

// 简化的Toast选项
export interface SimpleToastOptions {
  message: string;
  status?: NotificationStatus;
  duration?: number;
  position?: 'top-center' | 'top-right' | 'bottom-center' | 'bottom-right';
}

// 动画状态
export type AnimationState = 'entering' | 'entered' | 'exiting' | 'exited';

// 组件Props类型
export interface BaseNotificationProps {
  config: NotificationConfig;
  onClose: (id: string) => void;
  animationState: AnimationState;
}

export interface ModalProps extends BaseNotificationProps {
  config: ModalConfig;
}

export interface ToastProps extends BaseNotificationProps {
  config: ToastConfig;
}

// 主题配置
export interface NotificationTheme {
  colors: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    error: string;
    info: string;
  };
  spacing: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: string;
  shadow: string;
  backdrop: string;
}

// 默认配置
export const DEFAULT_TOAST_CONFIG: Partial<ToastConfig> = {
  duration: 3000,
  closable: true,
  position: 'top-center',
};

export const DEFAULT_MODAL_CONFIG: Partial<ModalConfig> = {
  backdrop: true,
  backdropClosable: true,
  escClosable: true,
  width: 'sm', // Use smaller default size for better mobile experience
  closable: true,
};

// 状态到颜色的映射
export const STATUS_COLOR_MAP: Record<NotificationStatus, string> = {
  loading: 'blue',
  success: 'green',
  error: 'red',
  warning: 'yellow',
  info: 'blue',
  confirm: 'blue',
};

// 模态类型到状态的映射
export const MODAL_TYPE_STATUS_MAP: Record<ModalType, NotificationStatus> = {
  confirm: 'confirm',
  alert: 'info',
  info: 'info',
  warning: 'warning',
  error: 'error',
  success: 'success',
};
