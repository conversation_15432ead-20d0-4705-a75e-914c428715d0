import React, { forwardRef } from 'react';
import { PhonePreviewProps } from '../../types/message';
import { formatDisplayTime } from '../../utils/time';
import MessageBubble from '../ui/MessageBubble';
import BatteryIcon from '../icons/BatteryIcon';

/**
 * PhonePreview 组件 - iPhone 风格的消息预览界面
 * 模拟真实的 iOS Messages 应用界面
 */
const PhonePreview = forwardRef<HTMLDivElement, PhonePreviewProps>(({
  recipientName,
  recipientAvatar,
  messages,
  deviceTime,
  timeFormat,
  mode,
  batteryPercentage = 85 // 默认电量85%
}, ref) => {
  // Theme variables (kept for potential future use)
  const textColor = mode === 'dark' ? 'text-white' : 'text-black';

  // SVG渐变背景 - 替代CSS渐变以兼容html2canvas
  const avatarGradientSvg = `data:image/svg+xml;base64,${btoa(`
    <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="avatarGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style="stop-color:#A5ABB9;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#858994;stop-opacity:1" />
        </linearGradient>
      </defs>
      <circle cx="20" cy="20" r="20" fill="url(#avatarGradient)" />
    </svg>
  `)}`;

  // 左侧返回箭头SVG图标 - 替代内联SVG以兼容html2canvas
  const backArrowSvg = `data:image/svg+xml;base64,${btoa(`
    <svg width="25" height="25" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M7.5 11.7486C7.5 12.0987 7.62428 12.39 7.91163 12.6718L14.5496 19.1656C14.7703 19.3921 15.0397 19.5 15.36 19.5C16.0116 19.5 16.535 18.9852 16.535 18.3419C16.535 18.016 16.3962 17.7248 16.1649 17.4931L10.2632 11.7459L16.1649 6.00414C16.399 5.76971 16.535 5.47528 16.535 5.15812C16.535 4.51201 16.0116 4 15.36 4C15.0369 4 14.7703 4.10782 14.5496 4.32854L7.91163 10.8254C7.62702 11.1014 7.50274 11.3958 7.5 11.7486Z" fill="#007AFF"/>
    </svg>
  `)}`;

  // 右侧视频通话SVG图标 - 替代内联SVG以兼容html2canvas
  const videoCallSvg = `data:image/svg+xml;base64,${btoa(`
    <svg width="25" height="25" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4.82117 19H13.7202C15.798 19 17.0414 17.7908 17.0414 15.7129V8.27859C17.0414 6.20925 15.798 5 13.7202 5H4.82117C2.83699 5 1.5 6.20925 1.5 8.27859V15.7129C1.5 17.7908 2.74332 19 4.82117 19ZM5.05962 17.7141C3.66302 17.7141 2.87105 16.9903 2.87105 15.517V8.48296C2.87105 7.00121 3.66302 6.27737 5.05962 6.27737H13.4818C14.8699 6.27737 15.6703 7.00121 15.6703 8.48296V15.517C15.6703 16.9903 14.8699 17.7141 13.4818 17.7141H5.05962ZM16.8455 9.61557V11.2336L20.899 7.88686C20.9756 7.82725 21.0267 7.78467 21.1034 7.78467C21.2056 7.78467 21.2481 7.86983 21.2481 7.98904V16.011C21.2481 16.1302 21.2056 16.2068 21.1034 16.2068C21.0267 16.2068 20.9756 16.1642 20.899 16.1131L16.8455 12.7664V14.3759L20.2603 17.2713C20.6009 17.5523 20.9756 17.7481 21.3248 17.7481C22.0742 17.7481 22.5681 17.1947 22.5681 16.4027V7.59733C22.5681 6.80535 22.0742 6.25183 21.3248 6.25183C20.9756 6.25183 20.6009 6.44769 20.2603 6.72871L16.8455 9.61557Z" fill="#007AFF"/>
    </svg>
  `)}`;

  return (
    <div
      ref={ref}
      className={`relative flex h-[684px] w-[316px] flex-col ${mode === 'dark' ? 'bg-black' : 'bg-white'} font-[Inter,_sans-serif] scale-75 xs:scale-90 sm:scale-100 overflow-hidden rounded-2xl border ${mode === 'dark' ? 'border-gray-700' : 'border-gray-100'} shadow-lg mx-auto`}
      style={{ fontFeatureSettings: 'normal' }}>

      {/* Unified Header Container - Status Bar + Chat Header */}
      <div
        style={{ backgroundColor: mode === 'dark' ? '#1C1C1E' : '#F6F5F6' }}
      >
        {/* iPhone Status Bar */}
        <div
          className="pb-[5px] pt-[13px]"
          style={{ backgroundColor: mode === 'dark' ? '#1C1C1E' : '#F6F5F6' }}
        >
          <div className={`flex items-center justify-between pl-[31px] pr-[25px] ${mode === 'dark' ? 'text-white' : 'text-black'}`}>
            <span className="text-[14px] font-semibold">{formatDisplayTime(deviceTime, timeFormat)}</span>
            <div className="flex items-center gap-[5px]">
              <svg viewBox="0 0 27 17" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-[10px]">
                <path d="M22.959 16.9531H25.332C25.9375 16.9531 26.3477 16.5234 26.3477 15.8887V1.06445C26.3477 0.429688 25.9375 0 25.332 0H22.959C22.3535 0 21.9336 0.429688 21.9336 1.06445V15.8887C21.9336 16.5234 22.3535 16.9531 22.959 16.9531Z" fill="currentColor"></path>
                <path d="M15.6543 16.9531H18.0078C18.6133 16.9531 19.0332 16.5234 19.0332 15.8887V4.92188C19.0332 4.28711 18.6133 3.85742 18.0078 3.85742H15.6543C15.0391 3.85742 14.6289 4.28711 14.6289 4.92188V15.8887C14.6289 16.5234 15.0391 16.9531 15.6543 16.9531Z" fill="currentColor"></path>
                <path d="M8.33984 16.9531H10.6934C11.3086 16.9531 11.7188 16.5234 11.7188 15.8887V8.45703C11.7188 7.82227 11.3086 7.39258 10.6934 7.39258H8.33984C7.72461 7.39258 7.31445 7.82227 7.31445 8.45703V15.8887C7.31445 16.5234 7.72461 16.9531 8.33984 16.9531Z" fill="currentColor"></path>
                <path d="M1.02539 16.9531H3.37891C3.99414 16.9531 4.4043 16.5234 4.4043 15.8887V11.5039C4.4043 10.8691 3.99414 10.4492 3.37891 10.4492H1.02539C0.410156 10.4492 0 10.8691 0 11.5039V15.8887C0 16.5234 0.410156 16.9531 1.02539 16.9531Z" fill="currentColor"></path>
              </svg>
              <svg viewBox="0 0 23 17" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-[11px]">
                <path d="M1.4761 6.84972C1.67141 7.03538 1.94485 7.03538 2.13039 6.83996C4.53274 4.28963 7.6968 2.94117 11.2417 2.94117C14.8062 2.94117 17.9898 4.2994 20.3726 6.84972C20.5484 7.02561 20.812 7.01584 21.0073 6.83018L22.355 5.48174C22.5308 5.30584 22.521 5.09088 22.3843 4.92477C20.0894 2.09106 15.773 0.00976562 11.2417 0.00976562C6.72024 0.00976562 2.3843 2.09106 0.0991432 4.92477C-0.0375756 5.09088 -0.0375756 5.30584 0.12844 5.48174L1.4761 6.84972Z" fill="currentColor"></path>
                <path d="M5.52878 10.9341C5.74362 11.1393 6.0073 11.11 6.20261 10.8951C7.37448 9.59548 9.28855 8.64766 11.2416 8.65743C13.2143 8.64766 15.1283 9.6248 16.3197 10.9244C16.4955 11.1295 16.7397 11.1198 16.9545 10.9244L18.4682 9.4196C18.6244 9.26326 18.644 9.04829 18.4975 8.87241C17.0229 7.0647 14.2885 5.70648 11.2416 5.70648C8.1948 5.70648 5.46042 7.0647 3.98581 8.87241C3.83933 9.04829 3.84909 9.24371 4.01511 9.4196L5.52878 10.9341Z" fill="currentColor"></path>
                <path d="M11.2417 16.2498C11.4566 16.2498 11.6421 16.1521 12.023 15.7808L14.4058 13.4942C14.5523 13.3477 14.5913 13.1327 14.4546 12.9568C13.8198 12.136 12.6187 11.4227 11.2417 11.4227C9.82571 11.4227 8.62453 12.1654 7.98977 13.0155C7.89211 13.1719 7.93117 13.3477 8.08742 13.4942L10.4605 15.7808C10.8413 16.1423 11.0269 16.2498 11.2417 16.2498Z" fill="currentColor"></path>
              </svg>
              {/* 电池图标 - 使用可配置的BatteryIcon组件 */}
              <BatteryIcon
                percentage={batteryPercentage}
                mode={mode}
                color="currentColor"
                className="h-[11px]"
              />
            </div>
          </div>
        </div>

        {/* Chat header */}
        <div
          className="pt-[22px]"
          style={{ backgroundColor: mode === 'dark' ? '#1C1C1E' : '#F6F5F6' }}
        >
          <div className="relative flex items-center justify-center px-[25px]">
            <div
              className="absolute left-[10px] size-[25px]"
              style={{
                backgroundImage: `url(${backArrowSvg})`,
                backgroundSize: 'contain',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center'
              }}
            ></div>
            <div className="size-[40px] overflow-hidden rounded-full">
              {recipientAvatar ? (
                <img src={recipientAvatar} alt="Avatar" className="w-full h-full object-cover" />
              ) : (
                <div
                  className="flex size-full items-center justify-center text-center"
                  style={{
                    backgroundImage: `url(${avatarGradientSvg})`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center'
                  }}
                >
                  <span className="text-[18px] font-semibold text-white">
                    {recipientName ? recipientName.charAt(0).toUpperCase() : 'P'}
                  </span>
                </div>
              )}
            </div>
            <div
              className="absolute right-[10px] size-[25px]"
              style={{
                backgroundImage: `url(${videoCallSvg})`,
                backgroundSize: 'contain',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center'
              }}
            ></div>
          </div>
          <div className="mx-auto mt-[2.5px] flex max-w-[65%] items-center justify-center overflow-hidden">
            <span className={`truncate text-[9px] ${mode === 'dark' ? 'text-white' : 'text-black'}`}>{recipientName || 'fake text message'}</span>
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="size-[9px] text-[#C1C1C3]">
              <path d="M16.535 11.7486C16.5295 11.3958 16.402 11.1014 16.1174 10.8254L9.48535 4.32854C9.25873 4.10782 8.99214 4 8.66634 4C8.02338 4 7.5 4.51201 7.5 5.15812C7.5 5.47528 7.63016 5.76971 7.8642 6.00414L13.7659 11.7459L7.8642 17.4931C7.6329 17.7248 7.5 18.016 7.5 18.3419C7.5 18.9852 8.02338 19.5 8.66634 19.5C8.98665 19.5 9.25873 19.3921 9.48535 19.1656L16.1174 12.6718C16.4048 12.39 16.535 12.0987 16.535 11.7486Z" fill="currentColor"></path>
            </svg>
          </div>
          <svg className="absolute inset-x-0 bottom-0" height="0.5" width="100%">
            <line className="stroke-[#B2B2B2]" x1="0" y1="0" x2="100%" y2="0" strokeWidth="1"></line>
          </svg>
        </div>
      </div>

      {/* Messages area - iOS authentic layout */}
      <div className={`no-scrollbar relative flex flex-1 flex-col overflow-auto px-[11px] pb-[81px] pt-[4px] ${mode === 'dark' ? 'bg-black' : 'bg-white'}`}>
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <p className={`${mode === 'dark' ? 'text-gray-400' : 'text-gray-500'} text-center text-sm`}>
              Add messages to see the preview
            </p>
          </div>
        ) : (
          <>
            {/* Time stamp at top - 使用第一个消息的时间戳 */}
            <div className="flex justify-center pb-[6px] pt-[6px] text-center">
              <span className="text-[9px] font-normal text-[#8D8D93]" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif' }}>
                {(() => {
                  // 格式化时间戳的函数（与 MessageBubble 中的逻辑保持一致）
                  const formatTimestamp = (date: Date) => {
                    const now = new Date();
                    const timeStr = date.toLocaleTimeString('en-US', {
                      hour: 'numeric',
                      minute: '2-digit',
                      hour12: timeFormat === '12'
                    });

                    // 检查是否是今天
                    const isToday = date.toDateString() === now.toDateString();
                    if (isToday) {
                      return `Today ${timeStr}`;
                    }

                    // 检查是否是昨天
                    const yesterday = new Date(now);
                    yesterday.setDate(now.getDate() - 1);
                    const isYesterday = date.toDateString() === yesterday.toDateString();
                    if (isYesterday) {
                      return `Yesterday ${timeStr}`;
                    }

                    // 检查是否在本周内（周日开始）
                    const startOfWeek = new Date(now);
                    const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
                    startOfWeek.setDate(now.getDate() - dayOfWeek);
                    startOfWeek.setHours(0, 0, 0, 0);

                    const endOfWeek = new Date(startOfWeek);
                    endOfWeek.setDate(startOfWeek.getDate() + 6);
                    endOfWeek.setHours(23, 59, 59, 999);

                    const isThisWeek = date >= startOfWeek && date <= endOfWeek;
                    if (isThisWeek) {
                      const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });
                      return `${dayName} ${timeStr}`;
                    }

                    // 其他时间：Tue, Jul 1 at 12:21 AM
                    const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
                    const monthDay = date.toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric'
                    });
                    return `${dayName}, ${monthDay} at ${timeStr}`;
                  };

                  // 使用第一个消息的时间戳，如果没有则使用当前时间
                  const firstMessage = messages[0];
                  if (firstMessage && firstMessage.timestamp && firstMessage.timestamp.trim() !== '') {
                    try {
                      const date = new Date(firstMessage.timestamp);
                      if (!isNaN(date.getTime())) {
                        return formatTimestamp(date);
                      }
                    } catch (error) {
                      console.warn('Invalid timestamp:', firstMessage.timestamp);
                    }
                  }

                  // 回退到当前时间
                  return formatTimestamp(new Date());
                })()}
              </span>
            </div>

            {/* Messages */}
            {messages.map((message, index) => {
              // 计算是否应该显示时间戳的逻辑
              // 如果前面任何一个消息没有自定义时间戳，那么当前消息及后续消息都不显示时间戳
              let shouldShowTimestamp = true;

              // 从第一个消息开始检查，如果发现任何一个消息没有自定义时间戳，
              // 那么从那个消息开始的所有后续消息都不显示时间戳
              for (let i = 0; i < index; i++) {
                const prevMessage = messages[i];
                if (!prevMessage.timestamp || prevMessage.timestamp.trim() === '') {
                  shouldShowTimestamp = false;
                  break;
                }
              }

              return (
                <MessageBubble
                  key={message.id}
                  message={message}
                  recipientName={recipientName}
                  recipientAvatar={recipientAvatar}
                  mode={mode}
                  timeFormat={timeFormat}
                  showTime={false} // We handle time display differently now
                  isFirstMessage={index === 0} // 标记第一个消息
                  shouldShowTimestamp={shouldShowTimestamp} // 传递时间戳显示控制
                />
              );
            })}
          </>
        )}
      </div>

      {/* Input area - iOS authentic design */}
      <div
        className="absolute inset-x-0 bottom-0 z-10 flex flex-col"
        style={{ backgroundColor: mode === 'dark' ? '#1C1C1E' : '#FCFCFC' }}
      >
        <div className="mb-[23px] flex items-center gap-[10px] px-[11px] pt-[4px]">
          {/* Plus button - Apple style */}
          <div
            className="flex size-[28px] items-center justify-center rounded-full"
            style={{ backgroundColor: '#E8E7EC', color: '#7E7F84' }}
          >
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="size-[14px]">
              <path d="M4 12.2616C4 12.9544 4.56808 13.5189 5.25732 13.5189H11.0074V19.269C11.0074 19.9551 11.5688 20.5232 12.2616 20.5232C12.9544 20.5232 13.5256 19.9551 13.5256 19.269V13.5189H19.269C19.9551 13.5189 20.5232 12.9544 20.5232 12.2616C20.5232 11.5688 19.9551 10.9976 19.269 10.9976H13.5256V5.25732C13.5256 4.56808 12.9544 4 12.2616 4C11.5688 4 11.0074 4.56808 11.0074 5.25732V10.9976H5.25732C4.56808 10.9976 4 11.5688 4 12.2616Z" fill="currentColor"/>
            </svg>
          </div>

          {/* Message input field with microphone icon */}
          <div
            className="flex flex-1 items-center overflow-hidden rounded-full px-[10px] py-[4.5px]"
            style={{
              border: `1px solid ${mode === 'dark' ? '#48484A' : '#DEDEDE'}`,
              backgroundColor: mode === 'dark' ? '#48484A' : 'white'
            }}
          >
            <span className="flex-1 truncate text-[13px] text-[#C1C2C4]"></span>
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="size-[16px] text-[#ACB4B7]">
              <path d="M5 11.6945C5 15.4993 7.52124 18.0717 11.0854 18.4399V20.0892H7.87046C7.3483 20.0892 6.90515 20.5177 6.90515 21.0491C6.90515 21.5718 7.3483 22 7.87046 22H16.1267C16.6517 22 17.0948 21.5718 17.0948 21.0491C17.0948 20.5177 16.6517 20.0892 16.1267 20.0892H12.9146V18.4399C16.4848 18.0717 19 15.4993 19 11.6945V9.95811C19 9.42964 18.574 9.02132 18.0463 9.02132C17.5213 9.02132 17.0838 9.42964 17.0838 9.95811V11.6294C17.0838 14.6721 15.0013 16.6593 12.003 16.6593C8.99865 16.6593 6.91616 14.6721 6.91616 11.6294V9.95811C6.91616 9.42964 6.48465 9.02132 5.95086 9.02132C5.42309 9.02132 5 9.42964 5 9.95811V11.6945ZM12.003 14.8612C13.8096 14.8612 15.2081 13.4822 15.2081 11.4521V5.41195C15.2081 3.37859 13.8096 2 12.003 2C10.1876 2 8.78063 3.37576 8.78063 5.40911V11.4521C8.78063 13.4822 10.1876 14.8612 12.003 14.8612Z" fill="currentColor"/>
              <path d="M5 11.6945C5 15.4993 7.52124 18.0717 11.0854 18.4399V20.0892H7.87046C7.3483 20.0892 6.90515 20.5177 6.90515 21.0491C6.90515 21.5718 7.3483 22 7.87046 22H16.1267C16.6517 22 17.0948 21.5718 17.0948 21.0491C17.0948 20.5177 16.6517 20.0892 16.1267 20.0892H12.9146V18.4399C16.4848 18.0717 19 15.4993 19 11.6945V9.95811C19 9.42964 18.574 9.02132 18.0463 9.02132C17.5213 9.02132 17.0838 9.42964 17.0838 9.95811V11.6294C17.0838 14.6721 15.0013 16.6593 12.003 16.6593C8.99865 16.6593 6.91616 14.6721 6.91616 11.6294V9.95811C6.91616 9.42964 6.48465 9.02132 5.95086 9.02132C5.42309 9.02132 5 9.42964 5 9.95811V11.6945ZM12.003 14.8612C13.8096 14.8612 15.2081 13.4822 15.2081 11.4521V5.41195C15.2081 3.37859 13.8096 2 12.003 2C10.1876 2 8.78063 3.37576 8.78063 5.40911V11.4521C8.78063 13.4822 10.1876 14.8612 12.003 14.8612Z" fill="currentColor"/>
            </svg>
          </div>
        </div>

        {/* Home indicator */}
        <div className="flex items-center justify-center pb-[6px]">
          <div className={`h-[4px] w-[112px] rounded-full ${mode === 'dark' ? 'bg-white' : 'bg-black'}`}></div>
        </div>
      </div>
    </div>
  );
});

PhonePreview.displayName = 'PhonePreview';

export default PhonePreview;
