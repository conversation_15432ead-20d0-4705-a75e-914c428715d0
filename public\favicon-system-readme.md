# Favicon 系统说明

## 概述
为 Fake Text Message Generator 项目创建了完整的现代 favicon 系统，采用 Apple 风格简约设计。

## 设计特色
- **主题**：聊天气泡图标，直观表达消息应用功能
- **色彩**：使用项目主色调蓝色 (#3B82F6)
- **风格**：Apple 风格简约设计，圆角、现代、清晰

## 文件结构
```
public/
├── favicon.svg                 # 主 SVG 图标 (32x32)
├── favicon-fallback.svg        # 备用 SVG 图标 (16x16)
├── favicon-16x16.svg          # 小尺寸图标
├── favicon-32x32.svg          # 标准尺寸图标
├── favicon-192x192.svg        # PWA 图标
├── favicon-512x512.svg        # 高分辨率图标
├── apple-touch-icon.svg       # Apple 设备图标 (180x180)
└── manifest.json              # Web App Manifest
```

## 浏览器支持
- ✅ 现代浏览器 (Chrome, Firefox, Safari, Edge)
- ✅ 移动设备 (iOS Safari, Android Chrome)
- ✅ PWA 支持
- ✅ Apple 设备优化

## 配置位置
所有 favicon 配置已添加到 `app/layout.tsx` 中，包括：
- 多尺寸 SVG 图标
- Apple Touch 图标
- Web App Manifest
- Microsoft 瓦片配置
- PWA 元数据

## 注意事项
1. 如需 ICO 格式支持，可使用在线工具将 `favicon-fallback.svg` 转换为 `favicon.ico`
2. 所有图标都使用 SVG 格式，确保在高分辨率设备上的清晰显示
3. 图标设计遵循 Apple 人机界面指南，适合各种设备和场景

## 维护
- 如需修改图标，建议先更新 `favicon.svg` 主文件
- 保持所有尺寸版本的设计一致性
- 确保新图标在小尺寸下仍然清晰可辨
