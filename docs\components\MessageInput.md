# MessageInput 组件文档

## 概述

`MessageInput` 是一个功能丰富的消息输入管理组件，提供多角色消息创建、图片上传、实时预览等功能。

## 功能特性

- ✅ **多角色支持** - 发送方、接收方、系统消息三种角色
- ✅ **图片上传** - 支持图片文件上传和预览
- ✅ **实时预览** - WYSIWYG 即时预览功能
- ✅ **消息管理** - 添加、编辑、删除消息
- ✅ **角色指示器** - 彩色圆点标识不同角色
- ✅ **高级时间管理** - 智能日期时间选择和自定义功能
- ✅ **响应式设计** - 适配移动端和桌面端

## 组件接口

```typescript
interface MessageInputProps {
  value?: string;                    // 输入值（向后兼容）
  onChange?: (value: string) => void; // 变更回调（向后兼容）
  onAddUser: () => void;             // 添加用户消息回调
  onAddRecipient: () => void;        // 添加接收方消息回调
  placeholder?: string;              // 占位符文本
  maxLength?: number;                // 最大字符长度
  disabled?: boolean;                // 是否禁用
  messages?: Message[];              // 消息列表（新功能）
  onMessagesChange?: (messages: Message[]) => void; // 消息变更回调（新功能）
}
```

## 使用示例

### 基础使用（向后兼容）

```typescript
import MessageInput from '../components/ui/MessageInput';

<MessageInput
  onAddUser={() => console.log('添加用户消息')}
  onAddRecipient={() => console.log('添加接收方消息')}
  placeholder="输入消息内容..."
  maxLength={500}
  disabled={false}
/>
```

### 完整功能使用

```typescript
import { useState } from 'react';
import MessageInput from '../components/ui/MessageInput';
import { Message } from '../types/message';

const [messages, setMessages] = useState<Message[]>([]);

<MessageInput
  messages={messages}
  onMessagesChange={setMessages}
  onAddUser={() => {
    // 自定义用户消息处理逻辑
    console.log('用户消息已添加');
  }}
  onAddRecipient={() => {
    // 自定义接收方消息处理逻辑
    console.log('接收方消息已添加');
  }}
  placeholder="输入消息内容..."
  maxLength={1000}
/>
```

### 禁用状态

```typescript
<MessageInput
  disabled={true}
  onAddUser={() => {}}
  onAddRecipient={() => {}}
  placeholder="消息输入已禁用"
/>
```

## 角色系统

### 角色类型

| 角色 | 颜色 | 标签 | 映射类型 |
|------|------|------|----------|
| 接收方 | 灰色 (#D1D1D3) | Recipient | recipient |
| 发送方 | 蓝色 (#007AFF) | User/Sender | user |
| 系统 | 绿色 (#34C759) | System/Third Party | system |

### 角色切换

```typescript
// 组件内部自动处理角色切换
// 用户可以通过角色指示器选择不同的消息类型
```

## 消息管理功能

### 添加消息

```typescript
// 文本消息
const addTextMessage = (content: string, actor: 'sender' | 'recipient' | 'system') => {
  const newMessage = {
    id: generateId(),
    actor,
    content,
    method: 'data' as const,
    status: 'default' as const
  };
  // 自动添加到消息列表
};

// 图片消息
const addImageMessage = (imageFile: File, actor: 'sender' | 'recipient' | 'system') => {
  const newMessage = {
    id: generateId(),
    actor,
    content: '',
    imageFile,
    imageUrl: URL.createObjectURL(imageFile),
    method: 'image' as const,
    status: 'default' as const
  };
  // 自动添加到消息列表
};
```

### 编辑消息

```typescript
// 点击消息内容可直接编辑
// 支持内联编辑，实时更新预览
```

### 删除消息

```typescript
// 点击三点菜单可删除消息
// 支持批量删除和单个删除
```

## 时间管理功能

### 智能日期选择器

MessageInput 组件集成了高级的时间管理功能，允许用户为每条消息设置自定义的时间戳。

```typescript
// 悬停触发日期选择器
<button
  onMouseEnter={handleDateHover}
  className="date-picker-trigger"
>
  Date
</button>

// 日期选择器组件
{showDatePicker && (
  <Calendar
    onDateSelect={handleDateSelect}
    onClose={() => setShowDatePicker(false)}
    disabled={disabled}
  />
)}
```

### 可编辑时间字段

日期选择器提供完全可编辑的时间字段：

```typescript
// 可编辑的日期时间状态
const [editableDateTime, setEditableDateTime] = useState({
  year: today.getFullYear().toString(),
  month: (today.getMonth() + 1).toString().padStart(2, '0'),
  day: today.getDate().toString().padStart(2, '0'),
  hours: today.getHours().toString().padStart(2, '0'),
  minutes: today.getMinutes().toString().padStart(2, '0')
});
```

### 时间验证和格式化

```typescript
// 实时验证日期时间有效性
const validateDateTime = () => {
  const { year, month, day, hours, minutes } = editableDateTime;
  const newDate = new Date(year, month - 1, day, hours, minutes, 0);

  // 检查日期是否被JavaScript自动调整
  if (newDate.getFullYear() !== year ||
      newDate.getMonth() !== (month - 1) ||
      newDate.getDate() !== day) {
    console.warn('Invalid date created');
    return false;
  }
  return true;
};
```

### 交互特性

- **悬停触发**: 鼠标悬停在 Date 按钮上自动显示日期选择器
- **点击外部关闭**: 点击日期选择器外部区域自动关闭
- **保持打开**: 选择日期后日期选择器保持打开状态，方便连续调整
- **实时更新**: 时间字段修改后立即生成新的日期时间字符串

### 时间格式

```typescript
// 生成 ISO 格式的日期时间字符串
const dateTimeString = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}:00`;

// 示例输出: "2024-01-25T14:30:00"
```

## 图片上传功能

### 支持的图片格式

- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- WebP (.webp)

### 图片处理

```typescript
// 自动生成预览URL
const imageUrl = URL.createObjectURL(file);

// 图片大小限制
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

// 图片预览
// 选择的图片会在图片图标区域显示预览
```

## 实时预览功能

### WYSIWYG 特性

- **即时同步**: 输入内容立即反映到预览区域
- **无需确认**: 不需要点击按钮或按回车键
- **实时更新**: 角色切换、内容修改即时生效

### 预览同步

```typescript
// 输入内容自动同步到父组件
useEffect(() => {
  if (onMessagesChange && messages) {
    onMessagesChange(messages);
  }
}, [messages, onMessagesChange]);
```

## 响应式设计

### 桌面端布局

```css
/* 多列表单布局 */
.desktop-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}
```

### 移动端布局

```css
/* 单列堆叠布局 */
.mobile-layout {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}
```

## 样式自定义

### CSS 变量

```css
:root {
  --message-input-border: #E5E7EB;
  --message-input-focus: #3B82F6;
  --message-input-bg: #FFFFFF;
  --message-input-text: #111827;
}
```

### 主题适配

```typescript
// 组件自动适配父组件的主题
// 支持明暗主题切换
```

## 性能优化

### 防抖处理

```typescript
// 输入内容防抖，减少不必要的更新
const debouncedUpdate = useMemo(
  () => debounce((value: string) => {
    // 更新逻辑
  }, 300),
  []
);
```

### 内存管理

```typescript
// 组件卸载时清理图片URL
useEffect(() => {
  return () => {
    draftMessages.forEach(msg => {
      if (msg.imageUrl && msg.imageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(msg.imageUrl);
      }
    });
  };
}, []);
```

## 通知集成

组件集成了通知系统，提供用户反馈：

```typescript
import { useNotification } from './notification';

const notification = useNotification();

// 成功提示
notification.toast.success('消息已添加');

// 错误提示
notification.toast.error('图片上传失败');
```

## 注意事项

1. **图片大小**: 建议图片文件不超过 10MB
2. **消息数量**: 单次建议不超过 50 条消息
3. **内存使用**: 及时清理不用的图片 URL
4. **兼容性**: 支持现代浏览器的文件 API

## 相关组件

- [`PhonePreview`](./PhonePreview.md) - 消息预览容器
- [`MessageBubble`](./MessageBubble.md) - 消息气泡组件
- [`useNotification`](../api/hooks.md) - 通知系统 Hook

## 扩展开发

### 添加新的消息类型

```typescript
// 在 types/message.ts 中扩展 Message 接口
interface Message {
  // 现有字段...
  audioUrl?: string;  // 音频消息
  videoUrl?: string;  // 视频消息
  location?: {        // 位置消息
    lat: number;
    lng: number;
    address: string;
  };
}
```

### 自定义角色配置

```typescript
// 修改 ROLE_CONFIG 常量
export const ROLE_CONFIG = {
  // 现有角色...
  custom: {
    color: 'rgb(255, 149, 0)', // 橙色
    label: 'Custom Role',
    mappedSender: 'system' as const
  }
};
```
