import { MetadataRoute } from 'next'

/**
 * 动态生成robots.txt
 * 
 * 优势：
 * - 支持环境变量配置
 * - 更精确的爬虫控制规则
 * - 自动适配不同环境（开发/生产）
 * - 符合Next.js最佳实践
 */
export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://faketextmessage.xyz'
  
  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/_next/',
          '/api/',
          '/*.json$',
          '/node_modules/',
          '/.next/',
          '/out/',
        ],
        crawlDelay: 1,
      },
      {
        userAgent: 'Googlebot',
        allow: '/',
        disallow: [
          '/_next/',
          '/api/',
          '/*.json$',
        ],
        crawlDelay: 1,
      },
      {
        userAgent: 'Bingbot',
        allow: '/',
        disallow: [
          '/_next/',
          '/api/',
          '/*.json$',
        ],
        crawlDelay: 1,
      },
      {
        userAgent: ['Slurp', 'DuckDuckBot'],
        allow: '/',
        disallow: [
          '/_next/',
          '/api/',
          '/*.json$',
        ],
        crawlDelay: 2,
      },
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
    host: baseUrl,
  }
}
