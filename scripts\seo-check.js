#!/usr/bin/env node

/**
 * SEO配置验证脚本
 * 
 * 功能：
 * - 验证必要的环境变量
 * - 检查SEO文件是否存在
 * - 验证配置的正确性
 * - 在部署前进行SEO检查
 */

const fs = require('fs');
const path = require('path');

// 手动加载环境变量
function loadEnvFile() {
  try {
    const envPath = '.env.local';
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf8');
      const lines = envContent.split('\n');

      lines.forEach(line => {
        line = line.trim();
        if (line && !line.startsWith('#') && line.includes('=')) {
          const [key, ...valueParts] = line.split('=');
          const value = valueParts.join('=');
          process.env[key.trim()] = value.trim();
        }
      });
    }
  } catch (error) {
    log(`⚠️  无法加载.env.local文件: ${error.message}`, 'yellow');
  }
}

// 加载环境变量
loadEnvFile();

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkEnvironmentVariables() {
  log('\n🔍 检查环境变量...', 'blue');
  
  const requiredVars = [
    'NEXT_PUBLIC_GA4_MEASUREMENT_ID'
  ];
  
  const optionalVars = [
    'NEXT_PUBLIC_SITE_URL',
    'NEXT_PUBLIC_GSC_VERIFICATION',
    'NEXT_PUBLIC_ENABLE_COOKIE_CONSENT'
  ];
  
  let hasErrors = false;
  
  // 检查必需的环境变量
  requiredVars.forEach(varName => {
    if (!process.env[varName]) {
      log(`❌ 缺少必需的环境变量: ${varName}`, 'red');
      hasErrors = true;
    } else {
      log(`✅ ${varName}: ${process.env[varName]}`, 'green');
    }
  });
  
  // 检查可选的环境变量
  optionalVars.forEach(varName => {
    if (process.env[varName]) {
      log(`✅ ${varName}: ${process.env[varName]}`, 'green');
    } else {
      log(`⚠️  可选环境变量未设置: ${varName}`, 'yellow');
    }
  });
  
  return !hasErrors;
}

function checkSEOFiles() {
  log('\n📁 检查SEO文件...', 'blue');
  
  const files = [
    { path: 'app/sitemap.ts', name: 'Dynamic Sitemap' },
    { path: 'app/robots.ts', name: 'Dynamic Robots.txt' },
    { path: 'public/lmt.txt', name: 'LMT Declaration' },
    { path: 'public/og-image.svg', name: 'Open Graph Image' },
    { path: 'components/seo/StructuredData.tsx', name: 'Structured Data' },
    { path: 'components/analytics/GoogleAnalytics.tsx', name: 'Google Analytics' },
    { path: 'components/analytics/CookieConsent.tsx', name: 'Cookie Consent' }
  ];
  
  let hasErrors = false;
  
  files.forEach(file => {
    if (fs.existsSync(file.path)) {
      log(`✅ ${file.name}: ${file.path}`, 'green');
    } else {
      log(`❌ 缺少文件: ${file.name} (${file.path})`, 'red');
      hasErrors = true;
    }
  });
  
  return !hasErrors;
}

function checkPackageJson() {
  log('\n📦 检查package.json配置...', 'blue');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    // 检查必要的依赖
    const requiredDeps = ['next', 'react', 'react-dom'];
    let hasErrors = false;
    
    requiredDeps.forEach(dep => {
      if (packageJson.dependencies && packageJson.dependencies[dep]) {
        log(`✅ 依赖: ${dep}@${packageJson.dependencies[dep]}`, 'green');
      } else {
        log(`❌ 缺少依赖: ${dep}`, 'red');
        hasErrors = true;
      }
    });
    
    // 检查构建脚本
    if (packageJson.scripts && packageJson.scripts.build) {
      log(`✅ 构建脚本: ${packageJson.scripts.build}`, 'green');
    } else {
      log(`❌ 缺少构建脚本`, 'red');
      hasErrors = true;
    }
    
    return !hasErrors;
  } catch (error) {
    log(`❌ 无法读取package.json: ${error.message}`, 'red');
    return false;
  }
}

function main() {
  log('🚀 开始SEO配置验证...', 'blue');
  
  const checks = [
    checkEnvironmentVariables(),
    checkSEOFiles(),
    checkPackageJson()
  ];
  
  const allPassed = checks.every(check => check);
  
  if (allPassed) {
    log('\n🎉 所有SEO检查通过！项目已准备好部署。', 'green');
    process.exit(0);
  } else {
    log('\n❌ SEO检查失败。请修复上述问题后重试。', 'red');
    process.exit(1);
  }
}

// 运行检查
main();
