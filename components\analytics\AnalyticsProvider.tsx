'use client';

import { useState, useEffect } from 'react';
import GoogleAnalytics from './GoogleAnalytics';
import CookieConsent from './CookieConsent';
import WebVitals from './WebVitals';

interface AnalyticsProviderProps {
  children: React.ReactNode;
}

/**
 * 分析服务提供者组件
 * 
 * 功能：
 * - 统一管理Google Analytics和Cookie同意
 * - 确保GDPR合规
 * - 提供分析服务的统一入口
 */
export default function AnalyticsProvider({ children }: AnalyticsProviderProps) {
  const [consentGranted, setConsentGranted] = useState<boolean | null>(null);
  const measurementId = process.env.NEXT_PUBLIC_GA4_MEASUREMENT_ID;

  useEffect(() => {
    // 在客户端检查现有的同意状态
    if (typeof window !== 'undefined') {
      const consent = localStorage.getItem('cookie-consent');
      setConsentGranted(consent === 'accepted');
    }
  }, []);

  const handleConsentChange = (consent: boolean) => {
    setConsentGranted(consent);
  };

  return (
    <>
      {children}
      
      {/* Google Analytics - 仅在有测量ID时加载 */}
      {measurementId && (
        <GoogleAnalytics 
          measurementId={measurementId}
          consentGranted={consentGranted === true}
        />
      )}
      
      {/* Cookie同意横幅 - 仅在启用时显示 */}
      {process.env.NEXT_PUBLIC_ENABLE_COOKIE_CONSENT !== 'false' && (
        <CookieConsent onConsentChange={handleConsentChange} />
      )}

      {/* Web Vitals监控 */}
      <WebVitals />
    </>
  );
}
