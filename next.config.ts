import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // SEO优化配置
  trailingSlash: false, // 确保URL一致性，不使用尾部斜杠

  // 静态文件优化
  async headers() {
    return [
      {
        // 为SEO相关文件设置适当的缓存头
        source: '/(robots.txt|sitemap.xml|lmt.txt)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=86400, s-maxage=86400', // 24小时缓存
          },
          {
            key: 'Content-Type',
            value: 'text/plain; charset=utf-8',
          },
        ],
      },
      {
        // 为静态资源设置长期缓存
        source: '/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable', // 1年缓存
          },
        ],
      },
      {
        // 安全头设置
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
      {
        // 为sitemap.xml设置XML内容类型
        source: '/sitemap.xml',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/xml; charset=utf-8',
          },
        ],
      },
    ];
  },

  // 重定向配置（如果需要）
  async redirects() {
    return [
      // 示例：将旧的URL重定向到新的URL
      // {
      //   source: '/old-path',
      //   destination: '/',
      //   permanent: true,
      // },
    ];
  },

  // 环境变量验证（开发时帮助）
  env: {
    // 这些环境变量将在构建时验证
    NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL || 'https://faketextmessage.xyz',
  },

  // 图片优化配置
  images: {
    // 如果使用外部图片域名，在这里配置
    domains: [],
    // 图片格式优化
    formats: ['image/webp', 'image/avif'],
    // 启用图片优化
    unoptimized: false,
  },

  // 性能优化配置
  poweredByHeader: false, // 移除X-Powered-By头
  compress: true, // 启用gzip压缩

  // 实验性功能（根据需要启用）
  experimental: {
    // 启用更好的SEO支持
    // optimizeCss: true, // 暂时禁用，可能导致构建问题
  },
};

export default nextConfig;
