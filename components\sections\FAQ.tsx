'use client';

import { useState } from 'react';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
}

const faqData: FAQItem[] = [
  {
    id: 'how-to-generate',
    question: 'How to generate fake texts?',
    answer: 'Creating fake text messages is easy with our Fake Text Generator. Start by customizing the recipient avatar and name, then add messages using our enhanced message input system. You can add text messages, upload images, select different message types (User, Recipient, System), and see everything update in real-time. Once you are happy with the result, you can download the fake text messages as a high-quality image.'
  },
  {
    id: 'messages-not-visible',
    question: 'Why aren\'t all my messages visible in the downloaded image?',
    answer: 'If you have multiple messages that extend beyond the visible area, you can scroll to position the messages exactly where you want them before downloading. The screenshot will capture the messages exactly as they appear in the preview, so simply scroll to your desired position and click the download button.'
  },
  {
    id: 'is-it-okay',
    question: 'Is it okay to use fake texts?',
    answer: 'Using fake text texts for parody, education, or mockups is generally acceptable. However, you should always make it clear that the fake texts are not real to avoid misinformation.'
  },
  {
    id: 'is-free',
    question: 'Is the Fake Text Generator free to use?',
    answer: 'Yes, the Fake Text Generator is completely free to use. We believe in offering valuable tools to enhance your creative process at no cost.'
  },
  {
    id: 'supported-devices',
    question: 'What devices are supported?',
    answer: 'Our fake text message generator works on all modern devices including desktop computers, tablets, and smartphones. The interface is fully responsive and optimized for mobile use.'
  },
  {
    id: 'privacy-security',
    question: 'Is my data safe and private?',
    answer: 'Yes, your privacy is our priority. All text messages and images are processed locally in your browser. We don\'t store any of your content on our servers, ensuring complete privacy and security.'
  },
  {
    id: 'image-quality',
    question: 'What is the quality of downloaded images?',
    answer: 'Downloaded images are high-resolution PNG files that maintain excellent quality for use in videos, presentations, or social media posts. The images are optimized for clarity and realistic appearance.'
  },
  {
    id: 'customization-options',
    question: 'What customization options are available?',
    answer: 'You can customize recipient names, upload custom avatars, choose between light and dark modes, set device time, adjust time format (12/24 hour), create unlimited message conversations, add images to messages, and select different message types (User, Recipient, System messages).'
  },
  {
    id: 'image-upload',
    question: 'Can I add images to fake text messages?',
    answer: 'Yes! You can upload images to your fake text messages by clicking the image icon in the message input area. Supported formats include JPG and PNG files up to 5MB. Images will appear in the message bubbles just like real iPhone messages.'
  },
  {
    id: 'message-types',
    question: 'What are the different message types available?',
    answer: 'You can create three types of messages: User messages (blue bubbles), Recipient messages (gray bubbles), and System messages (green bubbles). Select the type using the colored dot indicator before typing your message. This allows you to create more realistic and varied conversations.'
  },
  {
    id: 'real-time-preview',
    question: 'How does the real-time preview work?',
    answer: 'Our WYSIWYG (What You See Is What You Get) feature means that any changes you make in the message input area immediately appear in the iPhone preview. You don\'t need to click any buttons - just type, upload images, or change settings and see the results instantly.'
  }
];

export default function FAQ() {
  const [openItems, setOpenItems] = useState<Set<string>>(new Set());

  const toggleItem = (id: string) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(id)) {
      newOpenItems.delete(id);
    } else {
      newOpenItems.add(id);
    }
    setOpenItems(newOpenItems);
  };

  return (
    <section id="faq" className="py-16 bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Frequently Asked Questions
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Find answers to common questions about our fake text message generator
          </p>
        </div>

        <div className="space-y-4">
          {faqData.map((item) => {
            const isOpen = openItems.has(item.id);
            return (
              <div
                key={item.id}
                className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
              >
                <button
                  className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
                  onClick={() => toggleItem(item.id)}
                  aria-expanded={isOpen}
                  aria-controls={`faq-answer-${item.id}`}
                >
                  <h3 className="text-lg font-semibold text-gray-900 pr-4">
                    {item.question}
                  </h3>
                  <div className="flex-shrink-0">
                    <svg
                      className={`w-5 h-5 text-gray-500 transition-transform duration-200 ${
                        isOpen ? 'transform rotate-180' : ''
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </div>
                </button>
                {isOpen && (
                  <div
                    id={`faq-answer-${item.id}`}
                    className="px-6 pb-4 text-gray-600 leading-relaxed"
                  >
                    {item.answer}
                  </div>
                )}
              </div>
            );
          })}
        </div>

        <div className="mt-12 text-center">
          <p className="text-gray-600 mb-4">
            Still have questions? We're here to help!
          </p>
          <a
            href="#contact"
            className="inline-flex items-center px-6 py-3 bg-blue-500 text-white font-medium rounded-lg hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Contact Support
            <svg
              className="ml-2 w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M14 5l7 7m0 0l-7 7m7-7H3"
              />
            </svg>
          </a>
        </div>
      </div>
    </section>
  );
}
