import type { Metada<PERSON> } from "next";
import Link from "next/link";
import Header from "../../components/layout/Header";
import Footer from "../../components/layout/Footer";

export const metadata: Metadata = {
  title: "About Us - Fake Text Message Generator",
  description: "Discover our iPhone Messages interface generator with real-time WYSIWYG preview, image upload, and role-based messaging. Built with Next.js, TypeScript, and Apple-style design for creative professionals.",
  keywords: [
    "about",
    "fake text",
    "fake text message iphone",
    "text message generator",
    "iPhone Messages interface",
    "real-time preview",
    "WYSIWYG editor",
    "image upload",
    "fake text generator",
    "iOS 17 Messages",
    "role-based messaging",
    "Apple design",
    "Next.js project",
    "TypeScript",
    "creative tools"
  ],
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    title: "About Us - Fake Text Message Generator",
    description: "Learn about our fake text message generator project. Discover our mission, technology, and commitment to ethical creative tools.",
    type: "website",
  },
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://faketextmessage.xyz'}/about`,
  },
};

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Breadcrumb */}
        <nav className="mb-8" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-2 text-sm text-gray-500">
            <li>
              <Link href="/" className="hover:text-blue-600 transition-colors">
                Home
              </Link>
            </li>
            <li className="flex items-center">
              <svg className="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              <span className="text-gray-900">About Us</span>
            </li>
          </ol>
        </nav>

        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            About Our Project
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Creating ethical, privacy-first tools for digital content creators and educators.
          </p>
        </div>

        {/* Content */}
        <div className="prose prose-lg max-w-none">
          {/* Mission Statement */}
          <section className="mb-12">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-8 text-center">
              <h2 className="text-2xl font-semibold text-blue-900 mb-4 mt-0">
                Our Mission
              </h2>
              <p className="text-blue-800 text-lg mb-0">
                To provide creators, educators, and developers with powerful, ethical tools for generating
                realistic digital content while promoting responsible use and digital literacy.
              </p>
            </div>
          </section>

          {/* What We Do */}
          <section className="mb-12">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">
              What We Do
            </h2>
            <p className="text-gray-700 mb-6">
              Our Fake Text Message Generator is a free, browser-based tool that allows users to create
              realistic-looking text message conversations for legitimate creative and educational purposes.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-green-900 mb-3">
                  ✅ What We Support
                </h3>
                <ul className="text-green-800 space-y-2">
                  <li>• Creative content for videos and stories</li>
                  <li>• Educational digital literacy projects</li>
                  <li>• App mockups and UI/UX design</li>
                  <li>• Entertainment and artistic projects</li>
                  <li>• Personal creative endeavors</li>
                </ul>
              </div>

              <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-red-900 mb-3">
                  ❌ What We Oppose
                </h3>
                <ul className="text-red-800 space-y-2">
                  <li>• Fraud and deception</li>
                  <li>• Harassment and cyberbullying</li>
                  <li>• Identity theft and impersonation</li>
                  <li>• Misinformation and fake news</li>
                  <li>• Any illegal or harmful activities</li>
                </ul>
              </div>
            </div>
          </section>

          {/* Technology & Privacy */}
          <section className="mb-12">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">
              Technology & Privacy
            </h2>
            <p className="text-gray-700 mb-6">
              We've built our tool with privacy and security as core principles:
            </p>

            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                🔒 Privacy-First Design
              </h3>
              <ul className="text-gray-700 space-y-2">
                <li><strong>Local Processing:</strong> Everything happens in your browser - no server uploads</li>
                <li><strong>No Data Collection:</strong> We don't collect, store, or track personal information</li>
                <li><strong>No Registration:</strong> Use our tool instantly without creating accounts</li>
                <li><strong>GDPR Compliant:</strong> Built with European privacy standards in mind</li>
              </ul>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-4">
                ⚡ Modern Technology Stack
              </h3>
              <ul className="text-blue-800 space-y-2">
                <li><strong>Next.js 15:</strong> React framework with App Router for optimal performance</li>
                <li><strong>TypeScript:</strong> Type-safe development for reliability</li>
                <li><strong>Tailwind CSS:</strong> Modern, responsive design system</li>
                <li><strong>Client-Side Processing:</strong> All generation happens locally</li>
                <li><strong>Progressive Web App:</strong> Works offline and installs like a native app</li>
              </ul>
            </div>
          </section>

          {/* Features */}
          <section className="mb-12">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">
              Key Features
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-6 border border-gray-200 rounded-lg">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">iPhone 17/18 Interface</h3>
                <p className="text-gray-600 text-sm">Authentic iOS Messages design with Dynamic Island, status bar, and CSS pseudo-element message tails</p>
              </div>

              <div className="text-center p-6 border border-gray-200 rounded-lg">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Real-time WYSIWYG Preview</h3>
                <p className="text-gray-600 text-sm">What You See Is What You Get - instant preview with no delays or button clicks required</p>
              </div>

              <div className="text-center p-6 border border-gray-200 rounded-lg">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Multi-media Messages</h3>
                <p className="text-gray-600 text-sm">Upload images (JPEG, PNG, GIF, WebP) with instant preview and mixed content support</p>
              </div>

              <div className="text-center p-6 border border-gray-200 rounded-lg">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Smart Role System</h3>
                <p className="text-gray-600 text-sm">Color-coded role indicators: blue (sender), gray (recipient), green (system) with one-click switching</p>
              </div>

              <div className="text-center p-6 border border-gray-200 rounded-lg">
                <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Advanced Time Management</h3>
                <p className="text-gray-600 text-sm">Intelligent time input system with 12/24-hour format switching, editable date picker, input masking, and real-time validation</p>
              </div>

              <div className="text-center p-6 border border-gray-200 rounded-lg">
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Professional Export</h3>
                <p className="text-gray-600 text-sm">High-quality PNG download with perfect interface consistency and optimized screenshot quality</p>
              </div>

              <div className="text-center p-6 border border-gray-200 rounded-lg">
                <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Smart Battery Management</h3>
                <p className="text-gray-600 text-sm">Configurable battery percentage (0-100%) with intelligent validation, low battery warning, and authentic iPhone battery icon</p>
              </div>
            </div>
          </section>

          {/* Community & Support */}
          <section className="mb-12">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">
              Community & Support
            </h2>
            <p className="text-gray-700 mb-6">
              We believe in building a supportive community around our tools. We're committed to
              providing excellent user support and continuously improving based on user feedback.
            </p>

            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Get Support & Share Feedback
                  </h3>
                  <p className="text-gray-600 mb-4 sm:mb-0">
                    Need help or have suggestions? We're here to assist you and improve our tools based on your feedback.
                  </p>
                </div>
                <div className="flex space-x-4">
                  <Link
                    href="/contact"
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    Contact Us
                  </Link>
                </div>
              </div>
            </div>
          </section>

          {/* Values */}
          <section className="mb-12">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">
              Our Values
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <span className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-blue-600 font-bold">1</span>
                  </span>
                  Ethical Technology
                </h3>
                <p className="text-gray-700">
                  We build tools that empower creativity while actively discouraging harmful use.
                  Technology should enhance human potential, not enable deception.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <span className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-green-600 font-bold">2</span>
                  </span>
                  Privacy by Design
                </h3>
                <p className="text-gray-700">
                  Your privacy is not negotiable. We design our tools to work entirely locally,
                  ensuring your data never leaves your device.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <span className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-purple-600 font-bold">3</span>
                  </span>
                  Digital Literacy
                </h3>
                <p className="text-gray-700">
                  We promote awareness about digital manipulation and the importance of
                  verifying online content. Education is our best defense against misinformation.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <span className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-yellow-600 font-bold">4</span>
                  </span>
                  Accessibility
                </h3>
                <p className="text-gray-700">
                  Creative tools should be available to everyone. Our service is free,
                  works on any device, and requires no registration or personal information.
                </p>
              </div>
            </div>
          </section>

          {/* Contact */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">
              Get in Touch
            </h2>
            <p className="text-gray-700 mb-6">
              Have questions about our project, need support, or want to share feedback? We'd love to hear from you.
            </p>

            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-6">
              <Link
                href="/contact"
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                Contact Us
              </Link>

              <Link
                href="/help"
                className="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Help Center
              </Link>
            </div>
          </section>
        </div>
      </main>

      <Footer />
    </div>
  );
}