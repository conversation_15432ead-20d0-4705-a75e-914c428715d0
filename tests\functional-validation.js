/**
 * 功能验证脚本
 * 在浏览器控制台中运行，快速验证核心功能
 */

class FunctionalValidator {
  constructor() {
    this.results = [];
    this.errors = [];
  }

  /**
   * 记录测试结果
   */
  log(test, passed, message = '') {
    const result = {
      test,
      passed,
      message,
      timestamp: new Date().toISOString()
    };
    this.results.push(result);
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${test}: ${message}`);
    
    if (!passed) {
      this.errors.push(result);
    }
  }

  /**
   * 检查DOM元素是否存在
   */
  checkElement(selector, testName) {
    const element = document.querySelector(selector);
    const exists = element !== null;
    this.log(testName, exists, exists ? '元素存在' : `元素不存在: ${selector}`);
    return element;
  }

  /**
   * 检查元素是否可见
   */
  checkVisible(selector, testName) {
    const element = document.querySelector(selector);
    if (!element) {
      this.log(testName, false, `元素不存在: ${selector}`);
      return false;
    }
    
    const style = window.getComputedStyle(element);
    const visible = style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
    this.log(testName, visible, visible ? '元素可见' : '元素不可见');
    return visible;
  }

  /**
   * 检查函数是否存在
   */
  checkFunction(obj, funcName, testName) {
    const exists = typeof obj[funcName] === 'function';
    this.log(testName, exists, exists ? '函数存在' : `函数不存在: ${funcName}`);
    return exists;
  }

  /**
   * 验证页面基础结构
   */
  validatePageStructure() {
    console.log('🔍 验证页面基础结构...');
    
    // 检查主要容器
    this.checkElement('main', '主容器存在');
    this.checkElement('[data-testid="phone-preview"], .phone-preview, [class*="phone"]', 'iPhone预览组件');
    this.checkElement('input[type="text"], textarea', '输入组件');
    this.checkElement('button', '按钮组件');
    
    // 检查关键功能区域
    this.checkVisible('button[class*="download"], button:contains("Download")', '下载按钮可见');
    this.checkVisible('input[type="file"], [accept*="image"]', '文件上传组件');
  }

  /**
   * 验证React组件状态
   */
  validateReactComponents() {
    console.log('⚛️ 验证React组件...');
    
    // 检查React是否加载
    const reactExists = typeof window.React !== 'undefined' || document.querySelector('[data-reactroot]') !== null;
    this.log('React加载', reactExists, reactExists ? 'React已加载' : 'React未检测到');
    
    // 检查是否有React错误
    const errorBoundary = document.querySelector('[data-error-boundary]');
    this.log('无React错误', !errorBoundary, errorBoundary ? '检测到错误边界' : '无错误边界');
  }

  /**
   * 验证JavaScript功能
   */
  validateJavaScriptFunctions() {
    console.log('🔧 验证JavaScript功能...');
    
    // 检查必要的Web API
    this.log('Canvas API', typeof HTMLCanvasElement !== 'undefined', 'Canvas支持检查');
    this.log('File API', typeof File !== 'undefined' && typeof FileReader !== 'undefined', 'File API支持检查');
    this.log('URL API', typeof URL !== 'undefined' && typeof URL.createObjectURL === 'function', 'URL API支持检查');
    
    // 检查现代JavaScript特性
    this.log('Promise支持', typeof Promise !== 'undefined', 'Promise支持检查');
    this.log('Fetch API', typeof fetch === 'function', 'Fetch API支持检查');
  }

  /**
   * 验证样式和布局
   */
  validateStyles() {
    console.log('🎨 验证样式和布局...');
    
    // 检查CSS加载
    const stylesheets = document.styleSheets.length;
    this.log('CSS样式表加载', stylesheets > 0, `加载了${stylesheets}个样式表`);
    
    // 检查响应式设计
    const viewport = document.querySelector('meta[name="viewport"]');
    this.log('响应式设计配置', viewport !== null, viewport ? '视口配置正确' : '缺少视口配置');
    
    // 检查字体加载
    if (document.fonts) {
      document.fonts.ready.then(() => {
        this.log('字体加载', true, '字体加载完成');
      });
    }
  }

  /**
   * 验证错误处理
   */
  validateErrorHandling() {
    console.log('🛡️ 验证错误处理...');
    
    // 检查全局错误处理
    const hasErrorHandler = typeof window.onerror === 'function' || 
                           window.addEventListener.toString().includes('error');
    this.log('全局错误处理', hasErrorHandler, '错误处理机制检查');
    
    // 检查控制台错误
    const originalError = console.error;
    let errorCount = 0;
    console.error = function(...args) {
      errorCount++;
      originalError.apply(console, args);
    };
    
    setTimeout(() => {
      console.error = originalError;
      this.log('控制台无错误', errorCount === 0, `检测到${errorCount}个控制台错误`);
    }, 1000);
  }

  /**
   * 验证性能指标
   */
  validatePerformance() {
    console.log('⚡ 验证性能指标...');
    
    if (performance && performance.timing) {
      const timing = performance.timing;
      const loadTime = timing.loadEventEnd - timing.navigationStart;
      const domReady = timing.domContentLoadedEventEnd - timing.navigationStart;
      
      this.log('页面加载时间', loadTime < 5000, `加载时间: ${loadTime}ms`);
      this.log('DOM就绪时间', domReady < 3000, `DOM就绪: ${domReady}ms`);
    }
    
    // 检查内存使用（如果可用）
    if (performance.memory) {
      const memory = performance.memory;
      const memoryUsage = memory.usedJSHeapSize / 1024 / 1024;
      this.log('内存使用合理', memoryUsage < 100, `内存使用: ${memoryUsage.toFixed(2)}MB`);
    }
  }

  /**
   * 模拟用户交互测试
   */
  async simulateUserInteractions() {
    console.log('👆 模拟用户交互...');
    
    try {
      // 模拟点击测试
      const buttons = document.querySelectorAll('button:not([disabled])');
      this.log('可点击按钮', buttons.length > 0, `找到${buttons.length}个可点击按钮`);
      
      // 模拟输入测试
      const inputs = document.querySelectorAll('input[type="text"], textarea');
      if (inputs.length > 0) {
        const testInput = inputs[0];
        const originalValue = testInput.value;
        testInput.value = 'test';
        testInput.dispatchEvent(new Event('input', { bubbles: true }));
        
        // 恢复原值
        setTimeout(() => {
          testInput.value = originalValue;
          testInput.dispatchEvent(new Event('input', { bubbles: true }));
        }, 100);
        
        this.log('输入功能', true, '输入模拟成功');
      }
    } catch (error) {
      this.log('用户交互模拟', false, `交互测试失败: ${error.message}`);
    }
  }

  /**
   * 运行所有验证
   */
  async runAllValidations() {
    console.log('🚀 开始功能验证...\n');
    
    this.validatePageStructure();
    this.validateReactComponents();
    this.validateJavaScriptFunctions();
    this.validateStyles();
    this.validateErrorHandling();
    this.validatePerformance();
    await this.simulateUserInteractions();
    
    this.generateReport();
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    console.log('\n📊 测试报告');
    console.log('='.repeat(50));
    
    const total = this.results.length;
    const passed = this.results.filter(r => r.passed).length;
    const failed = this.errors.length;
    
    console.log(`总测试数: ${total}`);
    console.log(`通过: ${passed} ✅`);
    console.log(`失败: ${failed} ❌`);
    console.log(`成功率: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (this.errors.length > 0) {
      console.log('\n❌ 失败的测试:');
      this.errors.forEach(error => {
        console.log(`  • ${error.test}: ${error.message}`);
      });
    }
    
    console.log('\n💡 建议:');
    if (failed === 0) {
      console.log('  • 所有基础功能验证通过！');
      console.log('  • 建议进行手动测试以验证用户体验');
    } else {
      console.log('  • 请检查失败的测试项目');
      console.log('  • 建议在修复后重新运行验证');
    }
    
    // 返回结果供进一步处理
    return {
      total,
      passed,
      failed,
      successRate: (passed / total) * 100,
      errors: this.errors,
      results: this.results
    };
  }
}

// 使用说明
console.log(`
🧪 功能验证脚本使用说明:

1. 在浏览器控制台中运行以下命令开始验证:
   const validator = new FunctionalValidator();
   validator.runAllValidations();

2. 或者运行单个验证:
   validator.validatePageStructure();
   validator.validatePerformance();

3. 查看详细结果:
   console.table(validator.results);
`);

// 自动运行（可选）
if (typeof window !== 'undefined' && window.location.hostname !== '') {
  console.log('🔄 自动运行功能验证...');
  const autoValidator = new FunctionalValidator();
  autoValidator.runAllValidations();
}
