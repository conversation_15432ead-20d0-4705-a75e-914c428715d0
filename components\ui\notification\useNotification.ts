'use client';

import { useNotificationContext } from './NotificationProvider';
import {
  SimpleToastOptions,
  ConfirmOptions,
  AlertOptions,
  NotificationStatus,
} from './types';

/**
 * Hook for using the notification system
 * Provides simplified API for common notification operations
 */
export function useNotification() {
  const context = useNotificationContext();

  // Simplified toast methods
  const toast = {
    // Generic toast
    show: (message: string, options?: Partial<SimpleToastOptions>) => {
      return context.showToast({
        message,
        status: 'info',
        ...options,
      });
    },

    // Success toast
    success: (message: string, options?: Omit<SimpleToastOptions, 'status'>) => {
      return context.showToast({
        message,
        status: 'success',
        duration: 3000,
        ...options,
      });
    },

    // Error toast
    error: (message: string, options?: Omit<SimpleToastOptions, 'status'>) => {
      return context.showToast({
        message,
        status: 'error',
        duration: 5000, // Longer duration for errors
        ...options,
      });
    },

    // Warning toast
    warning: (message: string, options?: Omit<SimpleToastOptions, 'status'>) => {
      return context.showToast({
        message,
        status: 'warning',
        duration: 4000,
        ...options,
      });
    },

    // Info toast
    info: (message: string, options?: Omit<SimpleToastOptions, 'status'>) => {
      return context.showToast({
        message,
        status: 'info',
        duration: 3000,
        ...options,
      });
    },

    // Loading toast
    loading: (message: string, options?: Omit<SimpleToastOptions, 'status'>) => {
      return context.showToast({
        message,
        status: 'loading',
        duration: 0, // Don't auto-close loading toasts
        ...options,
      });
    },
  };

  // Modal methods
  const modal = {
    // Generic modal
    show: context.showModal,

    // Confirm dialog
    confirm: (message: string, options?: Partial<ConfirmOptions>): Promise<boolean> => {
      return context.showConfirm({
        message,
        title: 'Confirm',
        width: 'sm', // Use smaller size for confirm dialogs
        ...options,
      });
    },

    // Confirm with custom title
    confirmWithTitle: (title: string, message: string, options?: Partial<ConfirmOptions>): Promise<boolean> => {
      return context.showConfirm({
        title,
        message,
        width: 'sm', // Use smaller size for confirm dialogs
        ...options,
      });
    },

    // Danger confirm (for destructive actions)
    confirmDanger: (message: string, options?: Partial<ConfirmOptions>): Promise<boolean> => {
      return context.showConfirm({
        message,
        title: 'Warning',
        confirmText: 'Delete',
        variant: 'danger',
        width: 'sm', // Use smaller size for confirm dialogs
        ...options,
      });
    },

    // Alert dialog
    alert: (message: string, options?: Partial<AlertOptions>): Promise<void> => {
      return context.showAlert({
        message,
        title: 'Alert',
        ...options,
      });
    },

    // Success alert
    alertSuccess: (message: string, options?: Partial<AlertOptions>): Promise<void> => {
      return context.showAlert({
        message,
        title: 'Success',
        status: 'success',
        ...options,
      });
    },

    // Error alert
    alertError: (message: string, options?: Partial<AlertOptions>): Promise<void> => {
      return context.showAlert({
        message,
        title: 'Error',
        status: 'error',
        ...options,
      });
    },

    // Warning alert
    alertWarning: (message: string, options?: Partial<AlertOptions>): Promise<void> => {
      return context.showAlert({
        message,
        title: 'Warning',
        status: 'warning',
        ...options,
      });
    },

    // Info alert
    alertInfo: (message: string, options?: Partial<AlertOptions>): Promise<void> => {
      return context.showAlert({
        message,
        title: 'Information',
        status: 'info',
        ...options,
      });
    },
  };

  // Utility methods
  const utils = {
    // Hide specific notification
    hide: context.hide,

    // Hide all notifications
    hideAll: context.hideAll,

    // Get current notifications
    getNotifications: () => context.state.notifications,

    // Check if any notifications are visible
    hasNotifications: () => context.state.notifications.length > 0,

    // Check if any modals are visible
    hasModals: () => context.state.notifications.some(n => n.type === 'modal'),

    // Check if any toasts are visible
    hasToasts: () => context.state.notifications.some(n => n.type === 'toast'),
  };

  return {
    // Direct access to context methods
    ...context,
    
    // Simplified APIs
    toast,
    modal,
    utils,
  };
}

// Legacy compatibility - export individual methods
export const {
  showToast,
  showModal,
  showConfirm,
  showAlert,
  hide,
  hideAll,
} = {} as any; // Will be populated by the hook

// Default export
export default useNotification;
