'use client';

import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { BaseNotificationProps, AnimationState, NotificationStatus } from './types';

// Icon components for different statuses
const StatusIcon = ({ status }: { status: NotificationStatus }) => {
  const iconClasses = "w-5 h-5";
  
  switch (status) {
    case 'loading':
      return (
        <div className={`animate-spin rounded-full h-5 w-5 border-2 border-blue-500 border-t-transparent`} />
      );
    
    case 'success':
      return (
        <svg className={`${iconClasses} text-green-500`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      );
    
    case 'error':
      return (
        <svg className={`${iconClasses} text-red-500`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      );
    
    case 'warning':
      return (
        <svg className={`${iconClasses} text-yellow-500`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      );
    
    case 'info':
      return (
        <svg className={`${iconClasses} text-blue-500`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
    
    case 'confirm':
      return (
        <svg className={`${iconClasses} text-blue-500`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
    
    default:
      return null;
  }
};

// Get status color classes
const getStatusColor = (status: NotificationStatus) => {
  switch (status) {
    case 'loading':
    case 'info':
    case 'confirm':
      return 'text-blue-600';
    case 'success':
      return 'text-green-600';
    case 'error':
      return 'text-red-600';
    case 'warning':
      return 'text-yellow-600';
    default:
      return 'text-gray-600';
  }
};

// Base notification component
export function BaseNotification({ config, onClose, animationState }: BaseNotificationProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render on server or when not mounted
  if (!mounted) return null;

  const handleClose = () => {
    if (config.onClose) {
      config.onClose();
    }
    onClose(config.id);
  };

  // Toast positioning classes
  const getToastPositionClasses = () => {
    if (config.type !== 'toast') return '';
    
    const position = 'position' in config ? config.position : 'top-center';
    
    switch (position) {
      case 'top-center':
        return 'top-4 left-1/2 transform -translate-x-1/2';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-center':
        return 'bottom-4 left-1/2 transform -translate-x-1/2';
      case 'bottom-right':
        return 'bottom-4 right-4';
      default:
        return 'top-4 left-1/2 transform -translate-x-1/2';
    }
  };

  // Modal positioning classes
  const getModalPositionClasses = () => {
    if (config.type !== 'modal') return '';
    return 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2';
  };

  // Animation classes
  const getAnimationClasses = () => {
    const baseClasses = 'transition-all duration-300 ease-out';
    
    if (config.type === 'toast') {
      switch (animationState) {
        case 'entering':
        case 'entered':
          return `${baseClasses} translate-y-0 opacity-100`;
        case 'exiting':
        case 'exited':
          return `${baseClasses} -translate-y-full opacity-0`;
        default:
          return `${baseClasses} -translate-y-full opacity-0`;
      }
    } else {
      // Modal animations
      switch (animationState) {
        case 'entering':
        case 'entered':
          return `${baseClasses} scale-100 opacity-100`;
        case 'exiting':
        case 'exited':
          return `${baseClasses} scale-95 opacity-0`;
        default:
          return `${baseClasses} scale-95 opacity-0`;
      }
    }
  };

  // Width classes for modals
  const getModalWidthClasses = () => {
    if (config.type !== 'modal') return '';

    const width = 'width' in config ? config.width : 'md';

    switch (width) {
      case 'xs':
        return 'max-w-xs w-[85vw] sm:w-full';
      case 'sm':
        return 'max-w-sm w-[90vw] sm:w-full';
      case 'md':
        return 'max-w-md w-[90vw] sm:w-full';
      case 'lg':
        return 'max-w-lg w-[92vw] sm:w-full';
      case 'xl':
        return 'max-w-xl w-[95vw] sm:w-full';
      default:
        return 'max-w-md w-[90vw] sm:w-full';
    }
  };

  const content = (
    <>
      {/* Modal backdrop */}
      {config.type === 'modal' && (
        <div
          className={`fixed inset-0 bg-black/40 z-40 transition-opacity duration-300 ${
            animationState === 'entered' || animationState === 'entering' ? 'opacity-100' : 'opacity-0'
          }`}
          onClick={() => {
            if (config.type === 'modal' && 'backdropClosable' in config && config.backdropClosable) {
              handleClose();
            }
          }}
        />
      )}
      
      {/* Notification content */}
      <div 
        className={`
          fixed z-50
          ${config.type === 'toast' ? getToastPositionClasses() : getModalPositionClasses()}
          ${getAnimationClasses()}
        `}
        role={config.type === 'modal' ? 'dialog' : 'alert'}
        aria-live={config.type === 'toast' ? 'polite' : undefined}
        aria-modal={config.type === 'modal' ? 'true' : undefined}
        aria-labelledby={config.title ? `${config.id}-title` : undefined}
        aria-describedby={`${config.id}-message`}
      >
        <div className={`
          bg-white rounded-xl shadow-2xl p-4 sm:p-6
          border border-gray-100
          ${config.type === 'toast' ? 'max-w-sm sm:max-w-md w-[calc(100vw-2rem)] sm:w-full' : getModalWidthClasses()}
        `}>
          {/* Content */}
          <div className="flex items-start space-x-3">
            {/* Icon */}
            <div className="flex-shrink-0 mt-0.5">
              <StatusIcon status={config.status} />
            </div>
            
            {/* Text content */}
            <div className="flex-1 min-w-0">
              {config.title && (
                <h3 
                  id={`${config.id}-title`}
                  className="text-lg font-semibold text-gray-900 mb-2"
                >
                  {config.title}
                </h3>
              )}
              <p 
                id={`${config.id}-message`}
                className={`text-sm font-medium ${getStatusColor(config.status)} ${config.title ? '' : 'truncate'}`}
              >
                {config.message}
              </p>
            </div>
            
            {/* Close button for toasts */}
            {config.type === 'toast' && config.closable && config.status !== 'loading' && (
              <button
                onClick={handleClose}
                className="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-md hover:bg-gray-100"
                aria-label="Close notification"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
          
          {/* Modal buttons */}
          {config.type === 'modal' && 'buttons' in config && config.buttons && config.buttons.length > 0 && (
            <div className="mt-6 flex flex-col-reverse sm:flex-row sm:justify-end space-y-2 space-y-reverse sm:space-y-0 sm:space-x-3">
              {config.buttons.map((button, index) => (
                <button
                  key={index}
                  onClick={button.onClick}
                  className={`
                    px-4 py-2 rounded-lg font-medium text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2
                    ${button.variant === 'primary' 
                      ? 'bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-500' 
                      : button.variant === 'danger'
                      ? 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-500'
                      : 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500'
                    }
                  `}
                >
                  {button.text}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  );

  // 确保在客户端环境下才使用 createPortal
  if (typeof document !== 'undefined') {
    return createPortal(content, document.body);
  }

  // 服务端渲染时返回 null
  return null;
}

export default BaseNotification;
