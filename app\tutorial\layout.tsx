import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Interactive Tutorial | Fake Text Message Generator",
  description: "Learn how to create realistic iPhone text message conversations with our step-by-step interactive tutorial. Master all features in minutes.",
  keywords: [
    "fake text message tutorial",
    "iPhone message generator guide",
    "text conversation creator",
    "interactive tutorial",
    "step by step guide",
    "fake SMS tutorial",
    "message screenshot tutorial"
  ],
  authors: [{ name: "Fake Text Message Generator" }],
  creator: "Fake Text Message Generator",
  publisher: "Fake Text Message Generator",
  
  // Open Graph
  openGraph: {
    title: "Interactive Tutorial - Fake Text Message Generator",
    description: "Learn how to create realistic iPhone text message conversations with our comprehensive interactive tutorial.",
    url: "https://faketextmessage.xyz/tutorial",
    siteName: "Fake Text Message Generator",
    type: "website",
    locale: "en_US",
    images: [
      {
        url: "https://faketextmessage.xyz/images/tutorial-preview.jpg",
        width: 1200,
        height: 630,
        alt: "Interactive Tutorial Preview",
        type: "image/jpeg",
      }
    ],
  },
  
  // Twitter Card
  twitter: {
    card: "summary_large_image",
    title: "Interactive Tutorial - Fake Text Message Generator",
    description: "Learn how to create realistic iPhone text message conversations with our step-by-step tutorial.",
    images: ["https://faketextmessage.xyz/images/tutorial-preview.jpg"],
    creator: "@faketextmessage",
  },
  
  // Additional SEO
  alternates: {
    canonical: "https://faketextmessage.xyz/tutorial",
  },
  
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  
  verification: {
    google: process.env.NEXT_PUBLIC_GSC_VERIFICATION,
  },
  
  category: "Technology",
  classification: "Tutorial",
};

export default function TutorialLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "HowTo",
            "name": "How to Create Fake iPhone Text Messages",
            "description": "Step-by-step tutorial on creating realistic iPhone text message conversations",
            "image": "https://faketextmessage.xyz/images/tutorial-preview.jpg",
            "totalTime": "PT10M",
            "estimatedCost": {
              "@type": "MonetaryAmount",
              "currency": "USD",
              "value": "0"
            },
            "supply": [
              {
                "@type": "HowToSupply",
                "name": "Web Browser"
              },
              {
                "@type": "HowToSupply", 
                "name": "Internet Connection"
              }
            ],
            "tool": [
              {
                "@type": "HowToTool",
                "name": "Fake Text Message Generator"
              }
            ],
            "step": [
              {
                "@type": "HowToStep",
                "name": "Set Recipient Information",
                "text": "Enter the recipient's name and optionally upload an avatar image",
                "image": "https://faketextmessage.xyz/images/tutorial-step-1.jpg",
                "url": "https://faketextmessage.xyz/tutorial#step-1"
              },
              {
                "@type": "HowToStep", 
                "name": "Add Message Content",
                "text": "Type your message content and select the sender role",
                "image": "https://faketextmessage.xyz/images/tutorial-step-2.jpg",
                "url": "https://faketextmessage.xyz/tutorial#step-2"
              },
              {
                "@type": "HowToStep",
                "name": "Customize Device Settings", 
                "text": "Adjust time, battery level, and other iPhone interface elements",
                "image": "https://faketextmessage.xyz/images/tutorial-step-3.jpg",
                "url": "https://faketextmessage.xyz/tutorial#step-3"
              },
              {
                "@type": "HowToStep",
                "name": "Download Screenshot",
                "text": "Generate and download your realistic iPhone text message screenshot",
                "image": "https://faketextmessage.xyz/images/tutorial-step-4.jpg", 
                "url": "https://faketextmessage.xyz/tutorial#step-4"
              }
            ],
            "author": {
              "@type": "Organization",
              "name": "Fake Text Message Generator",
              "url": "https://faketextmessage.xyz"
            },
            "publisher": {
              "@type": "Organization", 
              "name": "Fake Text Message Generator",
              "url": "https://faketextmessage.xyz",
              "logo": {
                "@type": "ImageObject",
                "url": "https://faketextmessage.xyz/favicon-192x192.svg"
              }
            },
            "datePublished": "2025-01-31",
            "dateModified": "2025-01-31",
            "inLanguage": "en-US",
            "isAccessibleForFree": true,
            "learningResourceType": "Tutorial",
            "educationalLevel": "Beginner",
            "audience": {
              "@type": "Audience",
              "audienceType": "General Public"
            }
          })
        }}
      />
      {children}
    </>
  );
}
