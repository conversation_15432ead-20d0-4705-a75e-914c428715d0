import React from 'react';

interface DynamicIslandProps {
  deviceTime: string;
  mode: 'light' | 'dark';
  className?: string;
}

const DynamicIsland: React.FC<DynamicIslandProps> = ({
  deviceTime,
  mode,
  className = ''
}) => {
  const bgColor = mode === 'dark' ? 'bg-black' : 'bg-gray-900';
  const textColor = 'text-white';

  return (
    <div className={`relative px-6 py-3 ${className}`}>
      {/* Dynamic Island 胶囊形状 */}
      <div className={`${bgColor} rounded-full px-6 py-2 mx-auto w-fit flex items-center justify-between min-w-[200px]`}>
        {/* 时间显示 */}
        <span className={`text-sm font-semibold ${textColor}`}>
          {deviceTime}
        </span>
        
        {/* 右侧状态图标组 */}
        <div className="flex items-center space-x-2 ml-4">
          {/* 信号强度 - 现代信号条设计 */}
          <div className="flex items-end space-x-0.5">
            <div className={`w-1 h-2 ${textColor} bg-current rounded-full`}></div>
            <div className={`w-1 h-2.5 ${textColor} bg-current rounded-full`}></div>
            <div className={`w-1 h-3 ${textColor} bg-current rounded-full`}></div>
            <div className={`w-1 h-3.5 ${textColor} bg-current rounded-full`}></div>
          </div>
          
          {/* WiFi图标 - 简化设计 */}
          <svg className={`w-4 h-3 ${textColor}`} viewBox="0 0 16 12" fill="currentColor">
            <path d="M8 0C3.6 0 0 2.7 0 6c0 .3.2.5.5.5s.5-.2.5-.5c0-2.7 2.4-5 5-5s5 2.3 5 5c0 .3.2.5.5.5s.5-.2.5-.5c0-3.3-3.6-6-8-6z"/>
            <path d="M8 3c-1.7 0-3 1.3-3 3 0 .3.2.5.5.5s.5-.2.5-.5c0-1.1.9-2 2-2s2 .9 2 2c0 .3.2.5.5.5s.5-.2.5-.5c0-1.7-1.3-3-3-3z"/>
            <circle cx="8" cy="9" r="1"/>
          </svg>
          
          {/* 电池图标 - 现代设计 */}
          <div className="flex items-center">
            <div className={`w-6 h-3 border border-current rounded-sm ${mode === 'dark' ? 'bg-gray-800' : 'bg-gray-700'} relative`}>
              <div className={`w-4 h-1.5 ${textColor} bg-current rounded-sm absolute top-0.5 left-0.5`}></div>
            </div>
            <div className={`w-0.5 h-1.5 ${textColor} bg-current rounded-r-sm ml-0.5`}></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DynamicIsland;
