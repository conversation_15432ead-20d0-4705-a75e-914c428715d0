import { ChangelogEntry, FilterType, UpdateType } from '../types/changelog';

/**
 * 更新历史相关的工具函数
 */

/**
 * 按类型筛选更新条目
 */
export const filterEntriesByType = (
  entries: ChangelogEntry[],
  filter: FilterType
): ChangelogEntry[] => {
  if (filter === 'all') {
    return entries;
  }
  return entries.filter(entry => entry.type === filter);
};

/**
 * 按搜索关键词筛选更新条目
 */
export const filterEntriesBySearch = (
  entries: ChangelogEntry[],
  searchQuery: string
): ChangelogEntry[] => {
  if (!searchQuery.trim()) {
    return entries;
  }

  const query = searchQuery.toLowerCase().trim();
  return entries.filter(entry => 
    entry.title.toLowerCase().includes(query) ||
    entry.description.toLowerCase().includes(query) ||
    (entry.details && entry.details.some(detail => 
      detail.toLowerCase().includes(query)
    )) ||
    (entry.version && entry.version.toLowerCase().includes(query))
  );
};

/**
 * 组合筛选：按类型和搜索关键词
 */
export const filterEntries = (
  entries: ChangelogEntry[],
  filter: FilterType,
  searchQuery: string
): ChangelogEntry[] => {
  let filtered = filterEntriesByType(entries, filter);
  filtered = filterEntriesBySearch(filtered, searchQuery);
  return filtered;
};

/**
 * 计算各更新类型的数量
 */
export const calculateTypeCounts = (
  entries: ChangelogEntry[]
): Record<UpdateType, number> => {
  return entries.reduce((acc, entry) => {
    acc[entry.type] = (acc[entry.type] || 0) + 1;
    return acc;
  }, {} as Record<UpdateType, number>);
};

/**
 * 格式化日期显示
 */
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

/**
 * 格式化相对时间
 */
export const formatRelativeTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffInDays === 0) return '今天';
  if (diffInDays === 1) return '昨天';
  if (diffInDays < 7) return `${diffInDays}天前`;
  if (diffInDays < 30) return `${Math.floor(diffInDays / 7)}周前`;
  if (diffInDays < 365) return `${Math.floor(diffInDays / 30)}个月前`;
  return `${Math.floor(diffInDays / 365)}年前`;
};

/**
 * 获取最新更新的日期
 */
export const getLatestUpdateDate = (entries: ChangelogEntry[]): Date | null => {
  if (entries.length === 0) return null;
  
  // 假设条目已按日期倒序排列
  const latestEntry = entries[0];
  return new Date(latestEntry.date);
};

/**
 * 按年份分组更新条目
 */
export const groupEntriesByYear = (
  entries: ChangelogEntry[]
): Record<string, ChangelogEntry[]> => {
  return entries.reduce((acc, entry) => {
    const year = new Date(entry.date).getFullYear().toString();
    if (!acc[year]) {
      acc[year] = [];
    }
    acc[year].push(entry);
    return acc;
  }, {} as Record<string, ChangelogEntry[]>);
};

/**
 * 获取更新统计信息
 */
export const getUpdateStats = (entries: ChangelogEntry[]) => {
  const totalUpdates = entries.length;
  const latestUpdate = getLatestUpdateDate(entries);
  const typeStats = calculateTypeCounts(entries);
  const yearGroups = groupEntriesByYear(entries);
  
  return {
    totalUpdates,
    latestUpdate,
    typeStats,
    yearGroups,
    yearsActive: Object.keys(yearGroups).length
  };
};

/**
 * 验证更新条目数据
 */
export const validateChangelogEntry = (entry: Partial<ChangelogEntry>): boolean => {
  return !!(
    entry.id &&
    entry.date &&
    entry.type &&
    entry.title &&
    entry.description &&
    ['feature', 'fix', 'improvement', 'security'].includes(entry.type)
  );
};

/**
 * 排序更新条目（按日期倒序）
 */
export const sortEntriesByDate = (entries: ChangelogEntry[]): ChangelogEntry[] => {
  return [...entries].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );
};
