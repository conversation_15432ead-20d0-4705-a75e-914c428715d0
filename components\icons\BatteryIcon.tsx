import React from 'react';

interface BatteryIconProps {
  percentage?: number;
  charging?: boolean;
  className?: string;
  color?: string;
  mode?: 'light' | 'dark';
}

const BatteryIcon: React.FC<BatteryIconProps> = ({
  percentage = 85,
  charging = false,
  className = '',
  color = 'currentColor',
  mode = 'light'
}) => {
  // 计算电池填充宽度
  const fillWidth = Math.max(0, Math.min(100, percentage));
  const fillColor = percentage > 20 ? color : '#FF3B30'; // 低电量时显示红色

  return (
    <div className={`flex items-center ${className}`}>
      {/* 电池主体 - 使用您提供的 SVG */}
      <div className="relative h-[11px]">
        <svg viewBox="0 0 29 13" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-[11px]">
          <path d="M5.02041 12.6861H20.1843C21.9541 12.6861 23.2181 12.4875 24.1203 11.5853C25.0256 10.683 25.2114 9.4416 25.2114 7.66573V5.03555C25.2114 3.25967 25.0256 2.01221 24.1203 1.11299C23.215 0.210742 21.9541 0.0151367 20.1843 0.0151367H4.96455C3.26338 0.0151367 1.99639 0.213769 1.09414 1.11904C0.188867 2.02129 0 3.27246 0 4.97295V7.66573C0 9.4416 0.18584 10.686 1.08809 11.5853C1.99639 12.4875 3.2543 12.6861 5.02041 12.6861ZM4.76279 11.4771C3.61162 11.4771 2.5751 11.295 1.97334 10.7C1.38135 10.0982 1.20899 9.07754 1.20899 7.92334V4.83076C1.20899 3.62979 1.38135 2.59697 1.97031 1.99522C2.57207 1.39043 3.62442 1.2211 4.82236 1.2211H20.4486C21.5998 1.2211 22.6363 1.40625 23.2283 1.99824C23.8301 2.6 24.0024 3.61397 24.0024 4.76817V7.92334C24.0024 9.07754 23.8271 10.0982 23.2283 10.7C22.6363 11.298 21.5998 11.4771 20.4486 11.4771H4.76279ZM26.3428 8.72823C27.0658 8.68243 28.0435 7.75059 28.0435 6.34727C28.0435 4.94766 27.0658 4.01582 26.3428 3.97002V8.72823Z" fill="currentColor" opacity="0.25"></path>
        </svg>
        
        {/* 电量填充 - 长方形显示，定位在电池内部 */}
        <div 
          className="absolute top-[2.5px] left-[2.2px] h-[6px] transition-all duration-300 rounded-[1px]"
          style={{ 
            backgroundColor: fillColor,
            width: `${(fillWidth / 100) * 15.5}px`, // 根据SVG内部空间调整
          }}
        ></div>
        
        {/* 充电图标 */}
        {charging && (
          <div className="absolute inset-0 flex items-center justify-center">
            <svg className="w-2 h-2" viewBox="0 0 8 8" fill={mode === 'dark' ? '#000' : '#FFF'}>
              <path d="M4.5 0L2 3h1.5v2L6 2H4.5V0z"/>
            </svg>
          </div>
        )}
      </div>
    </div>
  );
};

export default BatteryIcon;
