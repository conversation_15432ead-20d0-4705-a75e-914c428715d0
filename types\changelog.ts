/**
 * 更新历史相关的类型定义
 */

// 更新类型
export type UpdateType = 'feature' | 'fix' | 'improvement' | 'security';

// 更新条目接口
export interface ChangelogEntry {
  id: string;
  date: string; // ISO 8601 格式
  version?: string; // 版本号（可选）
  type: UpdateType;
  title: string;
  description: string;
  details?: string[]; // 详细描述列表（可选）
  isExpanded?: boolean; // 是否展开详情
}

// 更新类型配置
export interface UpdateTypeConfig {
  type: UpdateType;
  label: string;
  icon: string; // SVG 路径或图标名称
  color: string; // Tailwind 颜色类
  bgColor: string; // 背景颜色类
  count: number; // 该类型的更新数量
}

// 筛选选项
export type FilterType = 'all' | UpdateType;

// 时间轴组件属性
export interface TimelineProps {
  entries: ChangelogEntry[];
  filter: FilterType;
  searchQuery: string;
  onFilterChange: (filter: FilterType) => void;
  onSearchChange: (query: string) => void;
  onToggleExpand: (id: string) => void;
}

// 时间轴条目组件属性
export interface TimelineEntryProps {
  entry: ChangelogEntry;
  onToggleExpand: (id: string) => void;
  isLast: boolean;
}

// 筛选器组件属性
export interface FilterBarProps {
  activeFilter: FilterType;
  updateTypes: UpdateTypeConfig[];
  onFilterChange: (filter: FilterType) => void;
}

// 搜索组件属性
export interface SearchBarProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  placeholder?: string;
}
